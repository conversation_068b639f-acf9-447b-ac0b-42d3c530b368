<?php

namespace App\Livewire\Backend\Settings\System\AllowedExternalIps;

use Livewire\Component;
use Livewire\Attributes\Locked;
use App\Facades\AllowedExternalIps\DeleteAllowedExternalIpFacade;
use App\Models\AllowedExternalIp;
use App\Livewire\Forms\Securities\RoadAccessForm;

class DeleteAllowedExternalIpsComponent extends Component
{
    #[Locked]
    public AllowedExternalIp $allowedExternalIp;
    public RoadAccessForm $roadAccessForm;
    public $revealDeleteAllowedExternalIpModal = false;

    public function openModalToDeleteAllowedExternalIp()
    {
        $this->roadAccessForm->generateTrackingTime();
        if ($this->roadAccessForm->checkUserAttempts('DeleteAllowedExternalIp:' . request()->ip(), 6, 300)) {return;}
        $this->revealDeleteAllowedExternalIpModal = true;
    }
    
    public function deleteAllowedExternalIp()
    {
        if ($this->roadAccessForm->checkUserAttempts('DeleteAllowedExternalIp:' . request()->ip(), 6, 300)) {return;}
        try {
            DeleteAllowedExternalIpFacade::execute($this->allowedExternalIp);
            $this->revealDeleteAllowedExternalIpModal = false;
            $this->dispatch('allowedExternalIpDeleted');
            $this->showSuccessSweetAlert('Deleted', 'External ip deleted successfully');
        } catch (\Throwable $th) {
            $this->dispatch('allowedExternalIpDeleteFailed');
        }
    }
    public function render()
    {
        return view('livewire.backend.settings.system.allowed-external-ips.delete-allowed-external-ips-component');
    }
}
