<?php

namespace App\Repositories\UntrustedHeaders;

use App\Models\UntrustedHeader;
use Illuminate\Support\Facades\Cache;
use App\Contracts\Repos\UntrustedHeadersRepos\GetUntrustedHeaderByIdRepositoryInterface;

final class CachedGetUntrustedHeaderByIdRepository implements GetUntrustedHeaderByIdRepositoryInterface
{
    public function __construct(private GetUntrustedHeaderByIdRepositoryInterface $getUntrustedHeaderByIdRepository) {}

    public function handle(int $id, array $selectedColumns = ['*']): ?UntrustedHeader
    {
        $fresh = 1800; // 30 minutes
        $stale = 3600; // 1 hour

        $columns = $selectedColumns;
        sort($columns); // normalize to avoid order mismatch
        $key = 'untrusted_header_id:' . $id . ':' . implode(',', $columns);

        return Cache::flexible($key, [$fresh, $stale], function () use ($id, $selectedColumns) {
            return $this->getUntrustedHeaderByIdRepository->handle($id, $selectedColumns);
        });
    }
}

