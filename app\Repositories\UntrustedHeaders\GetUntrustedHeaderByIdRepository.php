<?php

namespace App\Repositories\UntrustedHeaders;

use App\Models\UntrustedHeader;
use App\Contracts\Repos\UntrustedHeadersRepos\GetUntrustedHeaderByIdRepositoryInterface;

final class GetUntrustedHeaderByIdRepository  implements GetUntrustedHeaderByIdRepositoryInterface
{
    public function handle(int $id, array $selectedColumns = ['*']): ?UntrustedHeader
    {
        return UntrustedHeader::select($selectedColumns)->find($id);
    }
}
