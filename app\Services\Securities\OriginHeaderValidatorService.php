<?php

namespace App\Services\Securities;

use App\Models\BlockedIp;
use App\DTOs\BlockedIpDTO;
use Illuminate\Support\Facades\Request;
use App\Facades\BlockedIps\CreateBlockedIpFacade;
use App\Contracts\Securities\OriginHeaderValidatorServiceInterface;

final class OriginHeaderValidatorService implements OriginHeaderValidatorServiceInterface
{
    public function validate(): void
    {
        $ip = Request::ip();
        $validOrigin = config('app.url');
        $origin = Request::header('origin');
        $referer = Request::header('referer');

        $originIsValid = filled($origin) && $origin === $validOrigin;
        $refererIsValid = filled($referer) && str_contains($referer, $validOrigin);


        if (!$originIsValid) {
            $data = [
                'ip' => $ip,
                'reason' => 'Caught by origin header validator. Invalid origin',
            ];
            $dto = new BlockedIpDTO(...$data);
            CreateBlockedIpFacade::execute($dto);
            abort(403, 'Invalid Origin');
        }

        if ($referer && !$refererIsValid) {
            $data = [
                'ip' => $ip,
                'reason' => 'Caught by origin header validator. Invalid origin and referer',
            ];
            $dto = new BlockedIpDTO(...$data);
            CreateBlockedIpFacade::execute($dto);
            abort(403, 'Invalid Referer');
        }
    }
}
