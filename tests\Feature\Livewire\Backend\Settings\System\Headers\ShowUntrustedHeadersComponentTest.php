<?php

namespace Tests\Feature\Livewire\Backend\Settings\System\Headers;

use App\Livewire\Backend\Settings\System\Headers\ShowUntrustedHeadersComponent;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Livewire\Livewire;
use Tests\TestCase;

class ShowUntrustedHeadersComponentTest extends TestCase
{
    public function test_renders_successfully()
    {
        Livewire::test(ShowUntrustedHeadersComponent::class)
            ->assertStatus(200);
    }
}
