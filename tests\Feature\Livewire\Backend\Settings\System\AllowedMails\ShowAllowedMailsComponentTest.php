<?php

namespace Tests\Feature\Livewire\Backend\Settings\System\AllowedMails;

use App\Livewire\Backend\Settings\System\AllowedMails\ShowAllowedMailsComponent;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Livewire\Livewire;
use Tests\TestCase;

class ShowAllowedMailsComponentTest extends TestCase
{
    public function test_renders_successfully()
    {
        Livewire::test(ShowAllowedMailsComponent::class)
            ->assertStatus(200);
    }
}
