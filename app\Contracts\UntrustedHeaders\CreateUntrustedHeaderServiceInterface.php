<?php

namespace App\Contracts\UntrustedHeaders;

use App\Models\UntrustedHeader;
use App\DTOs\UntrustedHeaderDTO;
use Illuminate\Container\Attributes\Bind;
use Illuminate\Container\Attributes\Singleton;
use App\Services\UntrustedHeaders\CreateUntrustedHeaderService;

#[Bind(CreateUntrustedHeaderService::class)]
interface CreateUntrustedHeaderServiceInterface
{
    public function execute(UntrustedHeaderDTO $dto): UntrustedHeader;
}

