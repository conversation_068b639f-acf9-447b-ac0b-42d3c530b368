<?php

namespace App\Builders;

use Illuminate\Database\Eloquent\Builder;

class RoleBuilder extends Builder
{

    public function orderByIdAsc(): self
    {
        return $this->orderBy('id', 'asc');
    }
    public function orderByIdDesc(): self
    {
        return $this->orderBy('id', 'desc');
    }

    public function orderByNameAsc(): self
    {
        return $this->orderBy('name', 'asc');
    }
    public function orderByNameDesc(): self
    {
        return $this->orderBy('name', 'desc');
    }

    public function orderBySlugAsc(): self
    {
        return $this->orderBy('slug', 'asc');
    }
    public function orderBySlugDesc(): self
    {
        return $this->orderBy('slug', 'desc');
    }

    public function orderByTypeAsc(): self
    {
        return $this->orderBy('type', 'asc');
    }
    public function orderByTypeDesc(): self
    {
        return $this->orderBy('type', 'desc');
    }

    public function whereName(string $name): self
    {
        return $this->where('name', $name);
    }

    public function whereSlug(string $slug): self
    {
        return $this->where('slug', $slug);
    }

    public function whereType(int $type): self
    {
        return $this->where('type', $type);
    }
}
