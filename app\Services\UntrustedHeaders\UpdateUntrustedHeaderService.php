<?php

namespace App\Services\UntrustedHeaders;

use App\Models\UntrustedHeader;
use App\DTOs\UntrustedHeaderDTO;
use App\Actions\UntrustedHeaders\UpdateUntrustedHeaderAction;
use App\Contracts\UntrustedHeaders\UpdateUntrustedHeaderServiceInterface;

class UpdateUntrustedHeaderService implements UpdateUntrustedHeaderServiceInterface
{
    public function execute(UntrustedHeader $selectedUntrustedHeader, UntrustedHeaderDTO $dto): UntrustedHeader
    {
        return UpdateUntrustedHeaderAction::handle($selectedUntrustedHeader, $dto->toArray());
    }
}
