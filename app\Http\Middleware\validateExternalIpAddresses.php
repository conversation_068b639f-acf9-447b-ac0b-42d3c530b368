<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use App\Facades\Securities\AllowAuthorisedIpsFacade;

class validateExternalIpAddresses
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        if (AllowAuthorisedIpsFacade::allowAuthorised()) {
            return $next($request);
        }

        abort(403, 'Access denied from unauthorised external ip.');
    }
}
