<div>
    <x-button class="bg-slate-600 hover:bg-green-600" wire:click="openModalToCreateUntrustedHeader"
        target="openModalToCreateUntrustedHeader" icon="circle-plus">
        {{ __('Create Header') }}
    </x-button>

    <x-dialog-modal wire:model="revealCreateUntrustedHeaderModal" :maxWidth="'xl'">
        <x-slot name="content">
            <x-divider nameClass="text-blue-400">
                {{ __('CREATE UNTRUSTED HEADER') }}
            </x-divider>

            <x-road-access />
            <x-rate-limiter-message :message="$this->roadAccessForm->road_access_rate_limiter" />

            @if (!$this->roadAccessForm->road_access_rate_limiter)
                <x-lable-select-error parentClass="mt-2" labelClass="w-full text-slate-300"
                    selectClass="font-bold text-green-800" id="form.header_type" label="Header name"
                    model="form.header_type" placeholder="Select header name" :options="$this->headerNamesOptions" :value="$form->header_type"
                    modelModifier="live.debounce.500ms" />

                <x-lable-input-error id="form.pattern" type="text" label="Pattern" model="form.pattern"
                    inputType="input" value="{{ $form->pattern }}" :onEnter="$form->getCreateUntrustedHeaderEvent()"
                    placeholder="Enter header pattern...." modelModifier="live.debounce.400ms" inputClass="w-full"
                    icon="paw" iconClass="ps-2.5" parentClass="mt-1.5" />

                <x-lable-input-error id="form.description" type="text" label="Role description"
                    model="form.description" inputType="textarea" value="{{ $form->description }}" :onEnter="$form->getCreateUntrustedHeaderEvent()"
                    placeholder="Enter role description...." modelModifier="live.debounce.500ms" inputClass="w-full"
                    parentClass="mt-3" />
            @endif

            <div class="sm:flex items-center mt-4">
                <x-button class=" bg-red-500 hover:bg-red-600" wire:click="$toggle('revealCreateUntrustedHeaderModal')"
                    target="revealCreateUntrustedHeaderModal" icon='ban'>
                    {{ __('Cancel') }}
                </x-button>

                @if (!$this->roadAccessForm->road_access_rate_limiter)
                    <x-button class="ms-3  bg-green-500 hover:bg-green-600" wire:click="createUntrustedHeader"
                        target="createUntrustedHeader" :disabled="$form->hasErrors()" icon='cloud-arrow-up'>
                        {{ __('Create') }}
                    </x-button>
                @endif
                <x-action-message class="sm:ms-3 mt-2 sm:mt-0" on="untrustedHeaderCreated">
                    {{ __('Untrusted header created successfully.') }}
                </x-action-message>

                <x-action-message class="sm:ms-3 mt-2 sm:mt-0 bg-red-600 text-red-50"
                    on="untrustedHeaderCreationFailed">
                    {{ __('Error occured while trying to create untrusted header') }}
                </x-action-message>
            </div>

        </x-slot>
    </x-dialog-modal>

</div>
