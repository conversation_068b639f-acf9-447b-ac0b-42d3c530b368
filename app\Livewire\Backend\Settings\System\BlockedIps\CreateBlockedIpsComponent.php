<?php

namespace App\Livewire\Backend\Settings\System\BlockedIps;

use Livewire\Component;
use App\DTOs\BlockedIpDTO;
use App\Facades\Securities\HoneypotFacade;
use App\Facades\BlockedIps\CreateBlockedIpFacade;
use App\Livewire\Forms\BlockedIps\BlockedIpsForm;
use App\Livewire\Forms\Securities\RoadAccessForm;
use App\Facades\Securities\OriginHeaderValidatorFacade;

class CreateBlockedIpsComponent extends Component
{
    public BlockedIpsForm $form;
    public RoadAccessForm $roadAccessForm;

    public $revealCreateBlockedIpModal = false;
    public function openModalToCreateBlockedIp()
    {
        $this->resetErrorBag();
        $this->form->resetForm();
        $this->roadAccessForm->generateTrackingTime();
        if ($this->roadAccessForm->checkUserAttempts('CreateBlockedIp:' . request()->ip(), 6, 300)) {return;}
        $this->revealCreateBlockedIpModal = true;
    }

    public function createBlockedIp()
    {
        $validated = $this->form->validate();
        if ($this->roadAccessForm->checkUserAttempts('CreateBlockedIp:' . request()->ip(), 6, 300)) {return;}

        OriginHeaderValidatorFacade::validate();
        HoneypotFacade::inspect($this->roadAccessForm);
        $dto = new BlockedIpDTO(...$validated);

        try {
            CreateBlockedIpFacade::execute($dto);
            $this->form->resetForm();
            $this->dispatch('blockedIpCreated');
        } catch (\Throwable $th) {
            $this->dispatch('blockedIpCreationFailed');
        }
    }

    public function render()
    {
        return view('livewire.backend.settings.system.blocked-ips.create-blocked-ips-component');
    }
}
