<?php

namespace App\Repositories\BlockedIps;

use Illuminate\Support\Facades\Cache;
use App\Contracts\Repos\BlockedIpsRepos\CountBlockedIpsRepositoryInterface;

final class CachedCountBlockedIpsRepository implements CountBlockedIpsRepositoryInterface
{
    public function __construct(private CountBlockedIpsRepositoryInterface $countBlockedIpsRepository) {}

    public function handle(): int
    {
        $key   = 'blocked_ips:count';
        $fresh = 300; // 5 minutes
        $stale = 900; // 15 minutes

        return Cache::flexible($key, [$fresh, $stale], function () {
            return $this->countBlockedIpsRepository->handle();
        });
    }
}
