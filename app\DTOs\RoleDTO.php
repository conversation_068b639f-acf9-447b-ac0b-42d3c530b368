<?php

namespace App\DTOs;

use App\Enums\Roles\RoleIdsEnum;

class RoleDTO
{
    public function __construct(
        public readonly ?string $name = null,
        public readonly ?string $slug = null,
        public readonly int $type = RoleIdsEnum::GUEST->value,
        public readonly ?string $description = null

    ) {}

    public function toArray(): array
    {
        return array_filter([
            'name' => $this->name,
            'slug' => $this->slug,
            'type' => $this->type,
            'description' => $this->description
        ], fn($value) => !is_null($value));
    }
}
