<?php

namespace Tests\Feature\Livewire\Backend\Settings\System\Headers;

use App\Livewire\Backend\Settings\System\Headers\ViewUntrustedHeadersComponent;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Livewire\Livewire;
use Tests\TestCase;

class ViewUntrustedHeadersComponentTest extends TestCase
{
    public function test_renders_successfully()
    {
        Livewire::test(ViewUntrustedHeadersComponent::class)
            ->assertStatus(200);
    }
}
