<?php

namespace Database\Seeders;

use App\Models\Role;
use Illuminate\Database\Seeder;
use App\Enums\Roles\RoleTypesEnum;

class RoleSeeder extends Seeder
{
    public function run(): void
    {
        $roles = [
            // 🌐 PLATFORM ROLES
            [
                'name' => 'Guest',
                'slug' => 'guest',
                'type' => RoleTypesEnum::PLATFORM->value,
                'description' => "Provides temporary or limited access to the platform for external or trial users.
Can view specific dashboards or modules without making any modifications.
Ideal for demonstration purposes, auditing, or limited-time access to evaluate the system’s features without committing to a tenant account.",
            ],
            [
                'name' => 'Super Administrator',
                'slug' => 'super-administrator',
                'type' => RoleTypesEnum::PLATFORM->value,
                'description' => "Has unrestricted, system-wide access to all tenants and global configurations.
Can create, suspend, or delete tenant companies, configure platform settings, and override any role’s permissions.
This role is strictly for platform owners or top-level DevOps who oversee the SaaS infrastructure.",
            ],
            [
                'name' => 'Administrator',
                'slug' => 'administrator',
                'type' => RoleTypesEnum::PLATFORM->value,
                'description' => "Responsible for managing users, permissions, and modules within the platform.
Can assist in onboarding tenant companies, manage internal staff accounts, and handle standard operational tasks.
Cannot override the Super Administrator but is crucial for daily SaaS operations.",
            ],
            [
                'name' => 'System Administrator',
                'slug' => 'system-administrator',
                'type' => RoleTypesEnum::PLATFORM->value,
                'description' => "Responsible for the technical operations of the SaaS platform.
Handles deployments, server configurations, backups, monitoring, and system optimizations.
Works closely with developers and security personnel to ensure uptime, stability, and compliance with security protocols.",
            ],
            [
                'name' => 'Assistant',
                'slug' => 'assistant',
                'type' => RoleTypesEnum::PLATFORM->value,
                'description' => "Supports administrators or managers with clerical and operational tasks.
Can assist with scheduling, data entry, report preparation, and basic record management.
Perfect for administrative support roles that contribute to efficiency without accessing sensitive configurations.",
            ],
            [
                'name' => 'Editor',
                'slug' => 'editor',
                'type' => RoleTypesEnum::PLATFORM->value,
                'description' => "Responsible for creating and managing content within the platform.
Can edit, schedule, and manage documents, reports, or communication materials.
Frequently used by marketing, HR, or documentation teams to keep internal and public-facing content up-to-date.",
            ],
            [
                'name' => 'Security',
                'slug' => 'security',
                'type' => RoleTypesEnum::PLATFORM->value,
                'description' => "Handles the safety of physical or digital environments for the platform company.
May have access to security logs, alerts, surveillance data, or network monitoring tools depending on configuration.
Used for IT security specialists, building guards, or any role focused on safeguarding platform assets.",
            ],
            [
                'name' => 'Support',
                'slug' => 'support',
                'type' => RoleTypesEnum::PLATFORM->value,
                'description' => "Provides assistance to users, tenants, or internal staff.
Can access user accounts, troubleshoot issues, and provide guidance on platform features.
Ideal for customer service representatives, helpdesk staff, or technical support teams who need to resolve user queries without altering configurations.",
            ],
            [
                'name' => 'Developer',
                'slug' => 'developer',
                'type' => RoleTypesEnum::PLATFORM->value,
                'description' => "Responsible for building, testing, and maintaining the platform’s software.
Can access development tools, version control systems, and staging environments.
May also have permissions to deploy code, manage APIs, and collaborate with other developers.
This role is essential for software engineers, backend developers, and frontend developers who contribute to the platform’s functionality.",
            ],
            [
                'name' => 'Auditor',
                'slug' => 'auditor',
                'type' => RoleTypesEnum::PLATFORM->value,
                'description' => "Responsible for reviewing and verifying the platform’s compliance with standards.
Can access logs, reports, and user activities to ensure adherence to policies and regulations.
Ideal for internal or external auditors who need to assess the platform’s operations without making changes.
This role is crucial for maintaining transparency and accountability in the platform’s operations.",
            ],

            // 🏢 TENANT ROLE
            [
                'name' => 'Organisation Administrator',
                'slug' => 'organisation-administrator',
                'type' => RoleTypesEnum::ORGANISATION->value,
                'description' => "The primary administrative role within a tenant company.
Responsible for inviting users, assigning page-level and CRUD permissions, and managing the company’s internal settings.
Has no authority outside its own tenant and cannot access global platform operations.",
            ],
        ];

        $insertData = [];
        foreach ($roles as $role) {
            $insertData[] = [
                'name' => $role['name'],
                'slug' => $role['slug'],
                'type' => $role['type'],
                'description' => $role['description'],
                'created_at' => null,
                'updated_at' => null,
            ];
        }

        Role::insert($insertData);
    }
}
