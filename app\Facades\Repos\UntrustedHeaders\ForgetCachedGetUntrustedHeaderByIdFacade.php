<?php

namespace App\Facades\Repos\UntrustedHeaders;

use Illuminate\Support\Facades\Facade;
use App\Contracts\Repos\UntrustedHeadersRepos\ForgetCachedGetUntrustedHeaderByIdRepositoryInterface;

class ForgetCachedGetUntrustedHeaderByIdFacade extends Facade
{
    protected static function getFacadeAccessor(): string
    {
        return ForgetCachedGetUntrustedHeaderByIdRepositoryInterface::class;
    }
}
