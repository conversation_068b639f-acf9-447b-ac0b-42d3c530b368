<?php

namespace App\Repositories\UntrustedHeaders;

use Illuminate\Support\Facades\Cache;
use App\Contracts\Repos\UntrustedHeadersRepos\ForgetCachedGetUntrustedHeaderByIdRepositoryInterface;

final class ForgetCachedGetUntrustedHeaderByIdRepository implements ForgetCachedGetUntrustedHeaderByIdRepositoryInterface
{
    public function handle(int $id, array $selectedColumns = ['*']): void
    {
        $columns = $selectedColumns;
        sort($columns); // normalize to avoid order mismatch
        $key = 'untrusted_header_id:' . $id . ':' . implode(',', $columns);
        Cache::forget($key);
    }
}
