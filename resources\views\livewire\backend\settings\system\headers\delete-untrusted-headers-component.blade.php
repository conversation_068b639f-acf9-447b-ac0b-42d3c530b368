<div>
    <x-action-buttons :id="$header->id" deleteAction="openModalToDeleteUntrustedHeader" />

    <x-dialog-modal wire:model="revealDeleteUntrustedHeaderModal" :maxWidth="'xl'">
        <x-slot name="content">
            <x-divider nameClass="text-red-400">
                {{ __('DELETE UNTRUSTED HEADER') }}
            </x-divider>

            <x-road-access />
            <x-rate-limiter-message :message="$this->roadAccessForm->road_access_rate_limiter" />

            <x-notice class="text-red-50 bg-red-700  border mt-1 py-1" noticeClass="uppercase text-xs">
                <x-slot:notice>{{ __('Delete role') }}</x-slot>
                {{ 'Are you sure you want to delete ' . $header->header_type->label() . ' - ' . $header->pattern . '?' }}
            </x-notice>

            <div class="sm:flex items-center mt-4">
                <x-button class=" bg-red-500 hover:bg-red-600" wire:click="$toggle('revealDeleteUntrustedHeaderModal')"
                    target="revealDeleteUntrustedHeaderModal" icon='ban'>
                    {{ __('Cancel') }}
                </x-button>

                @if (!$this->roadAccessForm->road_access_rate_limiter)
                    <x-button class="ms-3  bg-green-500 hover:bg-green-600" wire:click="deleteUntrustedHeader"
                        target="deleteUntrustedHeader" icon='trash'>
                        {{ __('Delete') }}
                    </x-button>
                @endif

                <x-action-message class="sm:ms-3 mt-2 sm:mt-0" on="untrustedHeaderDeleted">
                    {{ __('Untrusted header deleted successfully.') }}
                </x-action-message>

                <x-action-message class="sm:ms-3 mt-2 sm:mt-0 bg-red-600 text-red-50" on="untrustedHeaderDeleteFailed">
                    {{ __('Error occured while trying to delete untrusted header') }}
                </x-action-message>
            </div>
        </x-slot>
    </x-dialog-modal>
</div>
