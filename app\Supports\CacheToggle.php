<?php

namespace App\Supports;

final class CacheToggle
{
    /**
     * Determine if caching is enabled for the given model key.
     *
     * @param string $modelKey  e.g. 'roles', 'permissions'
     * @return bool
     */
    public static function for(string $modelKey): bool
    {
        $global = config('repository.cache_enabled', true);

        if ($global) {
            return true;
        }

        return (bool) config("repository.models.$modelKey", false);
    }
}
