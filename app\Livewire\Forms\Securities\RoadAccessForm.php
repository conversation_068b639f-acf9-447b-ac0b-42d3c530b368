<?php

namespace App\Livewire\Forms\Securities;

use Carbon\Carbon;
use Livewire\Form;
use App\Facades\Securities\RateLimiterFacade;

class RoadAccessForm extends Form
{
    public $road_access_time;
    public $road_access_name = '';
    public $road_access_email = '';
    public $road_access_rate_limiter = '';


    public function resetTimeNameEmail()
    {
        $this->reset([
            'road_access_time',
            'road_access_name',
            'road_access_email',
        ]);
    }

    public function resetRateLimiter()
    {
        $this->reset('road_access_rate_limiter');
    }

    public function getTrackingTimeAttribute()
    {
        return $this->road_access_time = Carbon::now('Africa/Johannesburg')->timestamp;
    }
    // Populate random names for input fields
    public function generateTrackingTime()
    {
        $this->resetTimeNameEmail();
        $this->getTrackingTimeAttribute();
    }

    public function checkUserAttempts(string $key, int $maxAttempts, int $decaySeconds = 60)
    {
        $this->resetRateLimiter();

        $message = RateLimiterFacade::check($key, $maxAttempts, $decaySeconds);

        if ($message) {
            $this->road_access_rate_limiter = $message;
        }
    }
}
