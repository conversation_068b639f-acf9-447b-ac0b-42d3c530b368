<?php

namespace Tests\Feature\Livewire\Backend\Settings\System\BlockedIps;

use App\Livewire\Backend\Settings\System\BlockedIps\UpdateBlockedIpsComponent;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Livewire\Livewire;
use Tests\TestCase;

class UpdateBlockedIpsComponentTest extends TestCase
{
    public function test_renders_successfully()
    {
        Livewire::test(UpdateBlockedIpsComponent::class)
            ->assertStatus(200);
    }
}
