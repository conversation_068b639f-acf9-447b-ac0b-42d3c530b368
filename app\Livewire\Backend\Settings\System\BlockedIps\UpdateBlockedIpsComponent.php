<?php

namespace App\Livewire\Backend\Settings\System\BlockedIps;

use Livewire\Component;
use App\DTOs\BlockedIpDTO;
use Livewire\Attributes\Locked;
use App\Facades\Securities\HoneypotFacade;
use App\Facades\BlockedIps\UpdateBlockedIpFacade;
use App\Livewire\Forms\BlockedIps\BlockedIpsForm;
use App\Facades\Securities\OriginHeaderValidatorFacade;
use App\Livewire\Forms\Securities\RoadAccessForm;

class UpdateBlockedIpsComponent extends Component
{
    #[Locked]
    public $blockedIp;
    public BlockedIpsForm $form;
    public RoadAccessForm $roadAccessForm;
    public $revealUpdateBlockedIpModal = false;

    public function openModalToUpdateBlockedIp()
    {
        $this->resetErrorBag();
        $this->form->resetForm();
        $this->roadAccessForm->generateTrackingTime();
        if ($this->roadAccessForm->checkUserAttempts('UpdateBlockedIp:' . request()->ip(), 6, 300)) {
            return;
        }

        $this->form->selectedBlockedIp = $this->blockedIp;
        $this->form->ip_address = $this->blockedIp->ip_address;
        $this->form->reason = $this->blockedIp->reason;
        $this->revealUpdateBlockedIpModal = true;
    }

    public function updateBlockedIp()
    {
        $validated = $this->form->validate();
        if ($this->roadAccessForm->checkUserAttempts('UpdateBlockedIp:' . request()->ip(), 6, 300)) {
            return;
        }
        OriginHeaderValidatorFacade::validate();
        HoneypotFacade::inspect($this->roadAccessForm);
        $dto = new BlockedIpDTO(...$validated);

        try {
            UpdateBlockedIpFacade::execute($this->blockedIp, $dto);
            $this->dispatch('blockedIpUpdated');
        } catch (\Throwable $th) {
            $this->dispatch('blockedIpUpdateFailed');
        }
    }
    public function render()
    {
        return view('livewire.backend.settings.system.blocked-ips.update-blocked-ips-component');
    }
}
