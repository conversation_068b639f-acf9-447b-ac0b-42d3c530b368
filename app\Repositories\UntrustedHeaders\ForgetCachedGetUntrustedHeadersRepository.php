<?php

namespace App\Repositories\UntrustedHeaders;

use Illuminate\Support\Facades\Cache;
use App\Contracts\Repos\UntrustedHeadersRepos\ForgetCachedGetUntrustedHeadersRepositoryInterface;

final class ForgetCachedGetUntrustedHeadersRepository implements ForgetCachedGetUntrustedHeadersRepositoryInterface
{
    public function handle(int $limit = 10, array $selectedColumns = ['*']): void
    {
        $columns = $selectedColumns;
        sort($columns); // normalize to avoid order mismatch
        $key = 'untrusted_headers_list:' . $limit . ':' . implode(',', $columns);
        Cache::forget($key);
    }
}
