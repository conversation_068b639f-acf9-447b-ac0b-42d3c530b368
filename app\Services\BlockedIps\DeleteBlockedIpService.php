<?php

namespace App\Services\BlockedIps;

use App\Models\BlockedIp;
use App\Actions\BlockedIps\DeleteBlockedIpAction;
use App\Contracts\BlockedIps\DeleteBlockedIpServiceInterface;

class DeleteBlockedIpService implements DeleteBlockedIpServiceInterface
{
    public function execute(BlockedIp $blockedIp): bool
    {
        if (!$blockedIp) {
            return false;
        }
        return DeleteBlockedIpAction::handle($blockedIp);
    }
}
