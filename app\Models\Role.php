<?php

namespace App\Models;

use App\Builders\RoleBuilder;
use App\Enums\Roles\RoleTypeEnum;
use App\Casts\TransformStringCast;
use App\Enums\Roles\RoleTypesEnum;
use App\Traits\Defaults\HasFilterables;
use App\Traits\Defaults\HasSearchables;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Role extends Model
{

    use HasFactory;
    use HasSearchables;
    use HasFilterables;

    protected $fillable = ['name', 'slug', 'type', 'description'];
    protected $casts = [
        'id' => 'integer',
        'name' => TransformStringCast::class . ':capitalize',
        'slug' => 'string',
        'type' => RoleTypesEnum::class,
        'description' => TransformStringCast::class . ':ucfirst',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    protected $searchable = ['name', 'slug', 'description'];

    protected $filterable = [
        'filterByType' => 'type',
    ];

    public function newEloquentBuilder($query): Builder
    {
        return new RoleBuilder($query);
    }
}
