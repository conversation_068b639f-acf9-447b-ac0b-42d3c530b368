<div>
    <x-action-buttons :id="$role->id" deleteAction="openModalToDeleteRole" />

    <div>
        <x-dialog-modal wire:model.live="revealDeleteRoleModal" :maxWidth="'xl'">

            <x-slot name="content">
                <div class="p-2">
                    <x-divider nameClass="text-blue-300">
                        {{ __('DELETE ROLE') }}
                    </x-divider>
                    <x-road-access />
                    <x-rate-limiter-message :message="$this->roadAccessForm->road_access_rate_limiter" />

                    <x-notice class="text-red-50 bg-red-700  border mt-1 py-1" noticeClass="uppercase text-xs">
                        <x-slot:notice>{{ __('Delete role') }}</x-slot>
                        {{ 'Are you sure you want to delete ' . $role->name . ' role?' }}
                    </x-notice>

                    <div
                        class="flex flex-wrap items-center justify-end mt-4 space-y-2 sm:flex-nowrap sm:space-y-0 sm:space-x-4">
                        <div class="order-2 w-full text-center sm:w-auto sm:order-1">
                            <x-button wire:click="$toggle('revealDeleteRoleModal')" target="revealDeleteRoleModal"
                                class="text-red-100 bg-red-500  hover:bg-red-600 " icon="circle-xmark">
                                {{ 'Cancel' }}
                            </x-button>
                        </div>

                        @if (!$this->roadAccessForm->road_access_rate_limiter)
                            <div class="order-1 w-full text-center sm:w-auto sm:order-2">
                                <x-button class="text-green-100 bg-green-500 hover:bg-green-600 "
                                    wire:click="deleteRole" target="deleteRole" icon="trash">
                                    {{ 'Delete' }}
                                </x-button>
                            </div>
                        @endif

                        <x-action-message class="sm:ms-3 mt-2 sm:mt-0 bg-red-600 text-red-50" on="roleDeletionFailed">
                            {{ __('Error occurred while trying to delete role') }}
                        </x-action-message>
                    </div>
                </div>
            </x-slot>
        </x-dialog-modal>
    </div>

</div>
