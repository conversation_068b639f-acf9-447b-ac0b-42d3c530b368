<?php

namespace App\Services\Roles;

use App\Models\Role;
use App\Actions\Roles\DeleteRoleAction;
use App\Contracts\Roles\DeleteRoleServiceInterface;

class DeleteRoleService implements DeleteRoleServiceInterface
{
    public function execute(Role $selectedRole): bool
    {
        if (!$selectedRole) {
            return false;
        }

        return DeleteRoleAction::handle($selectedRole);
    }
}
