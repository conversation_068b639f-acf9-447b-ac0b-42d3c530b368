<?php

namespace App\Supports;

final class PaginatorSupport
{
    static public function getPaginationValues($totalRecords)
    {
        if ($totalRecords <= 10) {
            return [2, 2];
        } elseif ($totalRecords <= 50) {
            return [5, 5];
        } elseif ($totalRecords <= 100) {
            return [10, 10];
        } elseif ($totalRecords <= 200) {
            return [20, 20];
        } elseif ($totalRecords <= 300) {
            return [30, 30];
        } elseif ($totalRecords <= 500) {
            return [50, 50];
        } elseif ($totalRecords <= 700) {
            return [70, 70];
        } elseif ($totalRecords <= 1000) {
            return [100, 100];
        } else {
            return [150, 150];
        }
    }

    static public function generatePaginationOptions($initialPagination, $totalRecords, $intervalPagination)
    {
        for ($i = $initialPagination; $i < $totalRecords; $i += $intervalPagination) {
            yield $i;
        }
    }

    static public function generatePages($totalRecords)
    {
        // Ensure a valid number is provided
        if ($totalRecords <= 0) {
            return [];
        }

        [$initialPagination, $intervalPagination] = self::getPaginationValues($totalRecords);

        return iterator_to_array(
            self::generatePaginationOptions($initialPagination, $totalRecords, $intervalPagination)
        );
    }
}
