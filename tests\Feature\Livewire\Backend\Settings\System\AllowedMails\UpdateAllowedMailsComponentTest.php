<?php

namespace Tests\Feature\Livewire\Backend\Settings\System\AllowedMails;

use App\Livewire\Backend\Settings\System\AllowedMails\UpdateAllowedMailsComponent;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Livewire\Livewire;
use Tests\TestCase;

class UpdateAllowedMailsComponentTest extends TestCase
{
    public function test_renders_successfully()
    {
        Livewire::test(UpdateAllowedMailsComponent::class)
            ->assertStatus(200);
    }
}
