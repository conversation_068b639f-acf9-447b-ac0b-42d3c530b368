<?php

namespace Tests\Feature\Livewire\Backend\Settings\System\AllowedExternalIps;

use App\Livewire\Backend\Settings\System\AllowedExternalIps\CreateAllowedExternalIpsComponent;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Livewire\Livewire;
use Tests\TestCase;

class CreateAllowedExternalIpsComponentTest extends TestCase
{
    public function test_renders_successfully()
    {
        Livewire::test(CreateAllowedExternalIpsComponent::class)
            ->assertStatus(200);
    }
}
