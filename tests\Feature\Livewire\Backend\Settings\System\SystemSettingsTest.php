<?php

namespace Tests\Feature\Livewire\Backend\Settings\System;

use App\Livewire\Backend\Settings\System\SystemSettings;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Livewire\Livewire;
use Tests\TestCase;

class SystemSettingsTest extends TestCase
{
    public function test_renders_successfully()
    {
        Livewire::test(SystemSettings::class)
            ->assertStatus(200);
    }
}
