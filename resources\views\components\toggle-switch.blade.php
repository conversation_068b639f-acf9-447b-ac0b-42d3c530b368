@props([
    'checked' => false,
    'toggle' => null,
    'labelClass' => '',
])

<label class="inline-flex items-center cursor-pointer {{ $labelClass }}">
    <input type="checkbox" value="" class="sr-only peer" @if ($checked) checked @endif
         wire:click="{{ $toggle }}">
    <div
        class="relative w-11 h-6 bg-gray-200 rounded-full peer peer-focus:ring-4 peer-focus:ring-green-300
            dark:peer-focus:ring-green-800 dark:bg-red-700 peer-checked:after:translate-x-full
            rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-['']
            after:absolute after:top-0.5 after:start-[2px] after:bg-white after:border-gray-300 after:border
            after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-red-600 peer-checked:bg-green-600">
    </div>
</label>

