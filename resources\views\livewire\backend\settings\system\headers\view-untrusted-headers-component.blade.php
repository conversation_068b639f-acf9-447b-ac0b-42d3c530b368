<div>
    <x-action-buttons :id="$header->id" viewAction="openModalToViewUntrustedHeader" />



    <x-dialog-modal wire:model="revealViewUntrustedHeaderModal" :maxWidth="'xl'">
        <x-slot name="content">
            <x-divider nameClass="text-blue-400">
                {{ __('READ ABOUT HEADER') }}
            </x-divider>

            <div class="text-slate-200 text-base font-semibold my-2">
                Header name: {{ ucfirst($header->header_type->label())  }}
            </div>
            <div class="text-slate-200 text-sm font-semibold my-2">
                Header pattern: {{ $header->pattern }}
            </div>
            <x-notice class="bg-amber-100 text-amber-700 py-2 border mt-1 " noticeClass="uppercase text-xs">
                <x-slot:notice>Description:</x-slot>
                <div class="text-sm text-slate-800 my-2">
                    {{ $header->description }}
                </div>
            </x-notice>


            <div class="sm:flex items-center mt-4">
                <x-button class=" bg-yellow-600 hover:bg-yellow-700" wire:click="$toggle('revealViewUntrustedHeaderModal')"
                    target="revealViewUntrustedHeaderModal" icon="thumbs-up">
                    {{ __('Done') }}
                </x-button>
            </div>
        </x-slot>
    </x-dialog-modal>

</div>
