<?php

namespace App\Contracts\AllowedExternalIps;

use App\Models\AllowedExternalIp;
use App\DTOs\AllowedExternalIpDTO;
use App\Services\AllowedExternalIps\CreateAllowedExternalIpService;
use Illuminate\Container\Attributes\Bind;

#[Bind(CreateAllowedExternalIpService::class)]
interface CreateAllowedExternalIpServiceInterface
{
    public function execute(AllowedExternalIpDTO $dto): AllowedExternalIp;
}
