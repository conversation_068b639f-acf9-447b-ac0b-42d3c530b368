<?php

namespace App\Livewire\Backend\Settings\System\Roles;

use App\Models\Role;
use Livewire\Component;
use Livewire\Attributes\Locked;
use App\Facades\Roles\DeleteRoleFacade;
use App\Livewire\Forms\Securities\RoadAccessForm;

class DeleteRolesComponent extends Component
{
    #[Locked]
    public Role $role;
    public RoadAccessForm $roadAccessForm;

    public $revealDeleteRoleModal = false;


    public function openModalToDeleteRole()
    {
        $this->roadAccessForm->generateTrackingTime();
        $this->revealDeleteRoleModal = true;
    }

    public function deleteRole()
    {
        if ($this->roadAccessForm->checkUserAttempts('DeleteRole:' . request()->ip(), 6, 300)) {return;}
        try {
            DeleteRoleFacade::execute($this->role);
            $this->revealDeleteRoleModal = false;
            $this->dispatch('roleDeleted');
            $this->showSuccessSweetAlert('Deleted', 'Role deleted successfully');
        } catch (\Throwable $th) {
            $this->dispatch('roleDeletionFailed');
        }

    }
    public function render()
    {
        return view('livewire.backend.settings.system.roles.delete-roles-component');
    }
}
