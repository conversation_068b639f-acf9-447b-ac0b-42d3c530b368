<?php

use App\Enums\AllowedMails\AllowedMailStatusEnum;
use App\Enums\Roles\RoleTypesEnum;
use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('roles', function (Blueprint $table) {
            $table->id();
            $table->string('name')->unique();
            $table->string('slug')->unique();
            $table->smallInteger('type')->default(RoleTypesEnum::PLATFORM->value);
            $table->text('description');
            $table->timestamps();
        });

        Schema::create('untrusted_headers', function (Blueprint $table) {
            $table->id();
            $table->integer('header_type')->index();
            $table->string('pattern')->unique();
            $table->text('description');
            $table->timestamps();
        });

        Schema::create('blocked_ips', function (Blueprint $table) {
            $table->id();
            $table->string('ip_address')->unique();
            $table->text('reason');
            $table->timestamps();
        });

        Schema::create('allowed_external_ips', function (Blueprint $table) {
            $table->id();
            $table->string('ip_address')->unique();
            $table->string('description');
            $table->timestamps();
        });

        Schema::create('allowed_mails', function (Blueprint $table) {
            $table->id();
            $table->string('email')->unique();
            $table->smallInteger('status')->default(AllowedMailStatusEnum::AUTHORISED->value);
            $table->text('description');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('roles');
        Schema::dropIfExists('untrusted_headers');
        Schema::dropIfExists('blocked_ips');
        Schema::dropIfExists('allowed_external_ips');
        Schema::dropIfExists('allowed_mails');
    }
};
