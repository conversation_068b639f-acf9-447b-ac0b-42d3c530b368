<?php

namespace App\Livewire\Forms\Headers;

use Livewire\Form;
use App\Rules\GibberishRule;
use App\Models\UntrustedHeader;
use Livewire\Attributes\Validate;
use Illuminate\Validation\Rule;

class UntrustedHeadersForm extends Form
{
    #[Validate]
    public $header_type;
    #[Validate]
    public $pattern;
    #[Validate]
    public $description;

    public ?UntrustedHeader $selectedUntrustedHeader  = null;

    public function rules(): array
    {
        return [
            'header_type' => 'required|integer',
            'pattern' => array_filter([
                'required',
                'string',
                'min:3',
                'max:100',
                new GibberishRule('pattern', 3, 3),
                Rule::unique('untrusted_headers')->ignore($this->selectedUntrustedHeader)
            ]),
            'description' => array_filter([
                'required',
                'string',
                'min:5',
                'max:500',
                new GibberishRule('pattern', 3, 2)
            ]),
        ];
    }

    public function resetForm()
    {
        $this->reset(['header_type', 'pattern', 'description']);
    }

    public function hasErrors(): bool
    {
        return $this->getErrorBag()->isNotEmpty() || empty($this->header_type) || empty($this->pattern) || empty($this->description);
    }

    public function getCreateUntrustedHeaderEvent(): string
    {
        return $this->hasErrors() ? '' : 'createUntrustedHeader';
    }

    public function getUpdateUntrustedHeaderEvent(): string
    {
        return $this->hasErrors() ? '' : 'updateUntrustedHeader';
    }
}
