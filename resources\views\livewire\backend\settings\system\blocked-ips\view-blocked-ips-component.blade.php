<div>
    <x-action-buttons :id="$blockedIp->id" viewAction="openModalToViewBlockedIp" />

        <x-dialog-modal wire:model="revealViewBlockedIpModal" :maxWidth="'xl'">
            <x-slot name="content">
                <x-divider nameClass="text-blue-400">
                    {{ __('READ ABOUT BLOCKED IP ADDRESS') }}
                </x-divider>

                <div class="text-slate-200 text-base font-semibold my-2">
                    IP Address: {{ $blockedIp->ip_address }}
                </div>
                <x-notice class="bg-amber-100 text-amber-700 py-2 border mt-1 " noticeClass="uppercase text-xs">
                    <x-slot:notice>Reason to block:</x-slot>
                    <div class="text-sm text-slate-800 my-2">
                        {{ $blockedIp->reason }}
                    </div>
                </x-notice>

                <div class="sm:flex items-center mt-4">
                    <x-button class=" bg-yellow-600 hover:bg-yellow-700" wire:click="$toggle('revealViewBlockedIpModal')"
                        target="revealViewBlockedIpModal" icon="thumbs-up">
                        {{ __('Done') }}
                    </x-button>
                </div>
            </x-slot>
        </x-dialog-modal>
</div>
