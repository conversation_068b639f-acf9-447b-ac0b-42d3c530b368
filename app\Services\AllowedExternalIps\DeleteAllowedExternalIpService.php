<?php

namespace App\Services\AllowedExternalIps;

use App\Models\AllowedExternalIp;
use App\Actions\AllowedExternalIps\DeleteAllowedExternalIpAction;
use App\Contracts\AllowedExternalIps\DeleteAllowedExternalIpServiceInterface;

class DeleteAllowedExternalIpService implements DeleteAllowedExternalIpServiceInterface
{
    public function execute(AllowedExternalIp $selectedAllowedExternalIp): bool
    {
        if (!$selectedAllowedExternalIp) {
            return false;
        }
        return DeleteAllowedExternalIpAction::handle($selectedAllowedExternalIp);
    }
}
