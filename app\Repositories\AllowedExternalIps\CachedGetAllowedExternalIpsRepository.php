<?php

namespace App\Repositories\AllowedExternalIps;

use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Cache;
use App\Contracts\Repos\AllowedExternalIpsRepos\GetAllowedExternalIpsRepositoryInterface;

class CachedGetAllowedExternalIpsRepository implements GetAllowedExternalIpsRepositoryInterface
{
    public function __construct(private GetAllowedExternalIpsRepositoryInterface $getAllowedExternalIpsRepository) {}

    public function handle(int $limit = 10, array $selectedColumns = ['*']): Collection
    {
        $fresh = 300; // 5 minutes
        $stale = 900; // 15 minutes

        $columns = $selectedColumns;
        sort($columns);
        $key = 'allowed_external_ips_list:' . $limit . ':' . implode(',', $columns);

        return Cache::flexible($key, [$fresh, $stale], function () use ($limit, $selectedColumns) {
            return $this->getAllowedExternalIpsRepository->handle($limit, $selectedColumns);
        });
    }
}
