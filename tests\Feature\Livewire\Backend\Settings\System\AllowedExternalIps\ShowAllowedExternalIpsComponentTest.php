<?php

namespace Tests\Feature\Livewire\Backend\Settings\System\AllowedExternalIps;

use App\Livewire\Backend\Settings\System\AllowedExternalIps\ShowAllowedExternalIpsComponent;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Livewire\Livewire;
use Tests\TestCase;

class ShowAllowedExternalIpsComponentTest extends TestCase
{
    public function test_renders_successfully()
    {
        Livewire::test(ShowAllowedExternalIpsComponent::class)
            ->assertStatus(200);
    }
}
