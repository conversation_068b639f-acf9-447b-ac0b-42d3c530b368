<div>
    <x-action-buttons :id="$header->id" editAction="openModalToUpdateUntrustedHeader" />

    <x-dialog-modal wire:model="revealUpdateUntrustedHeaderModal" :maxWidth="'xl'">
        <x-slot name="content">
            <x-divider nameClass="text-blue-400">
                {{ __('UPDATE UNTRUSTED HEADER') }}
            </x-divider>

            <x-road-access />
            <x-rate-limiter-message :message="$this->roadAccessForm->road_access_rate_limiter" />

            @if (!$this->roadAccessForm->road_access_rate_limiter)
                <x-lable-select-error parentClass="mt-2" labelClass="w-full text-slate-300"
                    selectClass="font-bold text-green-800" id="headersForm.header_type" label="Header name"
                    model="headersForm.header_type" placeholder="Select header name" :options="$this->headerNamesOptions" :value="$headersForm->header_type"
                    modelModifier="live.debounce.500ms" />

                <x-lable-input-error id="headersForm.pattern" type="text" label="Pattern" model="headersForm.pattern"
                    inputType="input" value="{{ $headersForm->pattern }}" :onEnter="$headersForm->getUpdateUntrustedHeaderEvent()"
                    placeholder="Enter header pattern...." modelModifier="live.debounce.400ms" inputClass="w-full"
                    icon="paw" iconClass="ps-2.5" parentClass="mt-3" />

                <x-lable-input-error id="headersForm.description" type="text" label="Role description"
                    model="headersForm.description" inputType="textarea" value="{{ $headersForm->description }}"
                    :onEnter="$headersForm->getUpdateUntrustedHeaderEvent()" placeholder="Enter role description...." modelModifier="live.debounce.500ms"
                    inputClass="w-full" parentClass="mt-3" />
            @endif

            <div class="sm:flex items-center mt-4">
                <x-button class=" bg-red-500 hover:bg-red-600" wire:click="$toggle('revealUpdateUntrustedHeaderModal')"
                    target="revealUpdateUntrustedHeaderModal" icon='ban'>
                    {{ __('Cancel') }}
                </x-button>

                @if (!$this->roadAccessForm->road_access_rate_limiter)
                    <x-button class="ms-3  bg-green-500 hover:bg-green-600" wire:click="updateUntrustedHeader"
                        target="updateUntrustedHeader" :disabled="$headersForm->hasErrors()" icon='cloud-arrow-up'>
                        {{ __('Update') }}
                    </x-button>
                @endif

                <x-action-message class="sm:ms-3 mt-2 sm:mt-0" on="untrustedHeaderUpdated">
                    {{ __('Untrusted header updated successfully.') }}
                </x-action-message>

                <x-action-message class="sm:ms-3 mt-2 sm:mt-0 bg-red-600 text-red-50" on="untrustedHeaderUpdateFailed">
                    {{ __('Error occured while trying to update untrusted header') }}
                </x-action-message>
            </div>
        </x-slot>
    </x-dialog-modal>

</div>
