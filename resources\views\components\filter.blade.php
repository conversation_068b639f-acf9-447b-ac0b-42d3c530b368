@props([
    'filters' => [], // An array of filters with 'name', 'label', and 'options'
    'formWidth' => 'max-w-full',
    'results' => false,
])

@php
    $numFilters = count($filters);

    // Dynamic class for flex width based on the number of filters
    if ($numFilters == 1) {
        $flexClass = 'w-full';
    } elseif ($numFilters == 2) {
        $flexClass = 'md:w-1/2';
    } elseif ($numFilters == 3) {
        $flexClass = 'md:w-1/3';
    } elseif ($numFilters == 4) {
        $flexClass = 'md:w-1/4';
    } else {
        $flexClass = 'md:w-1/3';
    }

    // Decide whether to keep filters horizontally or stack them
    $filtersDirectionClass = $numFilters < 3 ? 'lg:flex-row' : '';
@endphp

@if ($results)
    <form class="{{ $formWidth }} mx-auto my-4">
        <div class="items-center md:flex {{ $filtersDirectionClass }} md:space-x-4 space-y-4 md:space-y-0">

            <!-- Dynamic Filters (All Select) -->
            @foreach ($filters as $filter)
                <div class="{{ $flexClass }}">
                    <label for="{{ $filter['name'] }}" class="sr-only">{{ $filter['label'] }}</label>
                    <select id="{{ $filter['name'] }}" wire:model.live="{{ $filter['name'] }}"
                        {{ empty($filter['options']) ? 'disabled' : '' }}
                        class="block w-full px-4 py-2 text-sm border rounded-md text-slate-200 focus:ring-amber-500 focus:border-amber-500
                    bg-slate-700 border-slate-600 placeholder-slate-200">
                        <option value="">{{ $filter['label'] }}</option>
                        @foreach ($filter['options'] as $key => $value)
                            <option value="{{ $key }}">{{ $value }}</option>
                        @endforeach
                    </select>
                </div>
            @endforeach
        </div>
    </form>
@endif
