<?php

namespace App\Builders;

use Illuminate\Database\Eloquent\Builder;

class AllowedMailBuilder extends Builder
{
    public function orderByIdAsc(): self
    {
        return $this->orderBy('id', 'asc');
    }
    public function orderByIdDesc(): self
    {
        return $this->orderBy('id', 'desc');
    }

    public function orderByEmailAsc(): self
    {
        return $this->orderBy('email', 'asc');
    }
    public function orderByEmailDesc(): self
    {
        return $this->orderBy('email', 'desc');
    }

    public function orderByStatusAsc(): self
    {
        return $this->orderBy('status', 'asc');
    }
    public function orderByStatusDesc(): self
    {
        return $this->orderBy('status', 'desc');
    }

    public function whereEmail(string $email): self
    {
        return $this->where('email', $email);
    }

    public function whereStatus(int $status): self
    {
        return $this->where('status', $status);
    }

}
