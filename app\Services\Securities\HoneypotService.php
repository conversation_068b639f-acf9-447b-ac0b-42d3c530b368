<?php

namespace App\Services\Securities;

use App\Models\BlockedIp;
use App\DTOs\BlockedIpDTO;
use App\Facades\BlockedIps\CreateBlockedIpFacade;
use App\Contracts\Securities\HoneypotServiceInterface;

class HoneypotService implements HoneypotServiceInterface
{

    public function inspect($form): void
    {
        $ip = request()->ip();

        $submittedTime = (int) ($form->road_access_time ?? 0);
        if ($submittedTime <= 0) {
            $data = [
                'ip' => $ip,
                'reason' => 'Caught by honeypot. Form submitted in zero seconds meaning the user is a bot or script.',
            ];
            $dto = new BlockedIpDTO(...$data);
            CreateBlockedIpFacade::execute($dto);
            abort(403, 'FORBIDDEN');
        }

        $timeElapsedInSeconds = time() - $submittedTime;

        if ($timeElapsedInSeconds < 1) {
            $data = [
                'ip' => $ip,
                'reason' => 'Caught by honeypot. Form submitted in less than one second meaning the user is a bot or script.',
            ];
            $dto = new BlockedIpDTO(...$data);
            CreateBlockedIpFacade::execute($dto);
            abort(403, 'FORBIDDEN - Your session has unusual activity');
        }

        if ($timeElapsedInSeconds > 600) {
            abort(403, 'FORBIDDEN - Your session has expired. Please try again.');
        }

        $honeypotFields = [
            'road_access_name' => $form->road_access_name ?? null,
            'road_access_email' => $form->road_access_email ?? null,
        ];

        foreach ($honeypotFields as $fieldName => $fieldValue) {
            if (!empty($fieldValue)) {
                $data = [
                    'ip' => $ip,
                    'reason' => "Caught by honeypot. Honeypot field $fieldName is filled with: $fieldValue",
                ];
                $dto = new BlockedIpDTO(...$data);
                CreateBlockedIpFacade::execute($dto);
                abort(403, 'FORBIDDEN GENUINE');
            }
        }
    }
}
