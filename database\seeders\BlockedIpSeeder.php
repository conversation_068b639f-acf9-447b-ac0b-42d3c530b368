<?php

namespace Database\Seeders;

use App\Models\BlockedIp;
use Illuminate\Database\Seeder;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;

class BlockedIpSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $blockedIps = [
            [
                'ip_address' => '**************',
                'reason' => 'Repeated SSH brute-force attempts across multiple global endpoints (honeypots reported invalid-user admin/1234).'
            ],
            [
                'ip_address' => '***********',
                'reason' => 'Multiple aggressive SSH brute-force login attempts with invalid usernames (e.g., ypwang, leader).'
            ],
            [
                'ip_address' => '************',
                'reason' => 'High-volume SSH login failures targeting multiple accounts across several regions.'
            ],
            [
                'ip_address' => '************',
                'reason' => 'Sustained credential stuffing attacks on SSH and web login forms.'
            ],
            [
                'ip_address' => '**************',
                'reason' => 'Detected in multiple phishing campaigns and port scanning activities.'
            ],
            [
                'ip_address' => '************',
                'reason' => 'Repeated attempts to exploit known CMS vulnerabilities (<PERSON><PERSON><PERSON>, Jo<PERSON>la).'
            ],
            [
                'ip_address' => '*************',
                'reason' => 'High-volume SMTP spam relay attempts detected by multiple honeypots.'
            ],
            [
                'ip_address' => '************',
                'reason' => 'Mass SSH brute force detected targeting root and admin accounts.'
            ],
            [
                'ip_address' => '***************',
                'reason' => 'Involved in SQL injection attacks against e-commerce sites.'
            ],
            [
                'ip_address' => '**************',
                'reason' => 'Detected scanning for open RDP ports and attempting unauthorized logins.'
            ],
            [
                'ip_address' => '************',
                'reason' => 'Observed distributing malware payloads through compromised web servers.'
            ],
        ];

        $insertData = [];
        foreach ($blockedIps as $blockedIp) {
            $insertData[] = [
                'ip_address' => $blockedIp['ip_address'],
                'reason' => $blockedIp['reason'],
                'created_at' => now(),
                'updated_at' => now(),
            ];
        }

        BlockedIp::insert($insertData);
    }
}
