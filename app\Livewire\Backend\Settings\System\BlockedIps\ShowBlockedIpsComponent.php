<?php

namespace App\Livewire\Backend\Settings\System\BlockedIps;

use Livewire\Component;
use App\Models\BlockedIp;
use Livewire\Attributes\On;
use Livewire\Attributes\Computed;
use App\Supports\PaginatorSupport;
use App\Facades\Repos\BlockedIps\CountBlockedIpsFacade;

class ShowBlockedIpsComponent extends Component
{
    public $blockedIpsSearch = '';
    public $blockedIpsPaginate = 5;


    #[Computed]
    public function blockedIps()
    {
        $selectedColumns = ['id', 'ip_address', 'reason'];
        $searchTerm = trim('%' . $this->blockedIpsSearch . '%');

        $query = BlockedIp::query();
        $query = $query->select($selectedColumns);
        $query = $query->search($searchTerm);
        $query = $query->orderByIdDesc();
        $query = $query->paginate($this->blockedIpsPaginate);

        return $query;
    }

    #[Computed]
    public function totalBlockedIps()
    {
        return CountBlockedIpsFacade::handle();
    }

    #[Computed]
    public function blockedIpsPages(): array
    {
        return PaginatorSupport::generatePages($this->totalBlockedIps);
    }


    #[On(['blockedIpCreated', 'blockedIpUpdated', 'blockedIpDeleted'])]
    public function render()
    {
        return view('livewire.backend.settings.system.blocked-ips.show-blocked-ips-component');
    }
}
