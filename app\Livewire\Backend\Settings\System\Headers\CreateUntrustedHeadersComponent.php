<?php

namespace App\Livewire\Backend\Settings\System\Headers;

use Livewire\Component;
use Livewire\WithPagination;
use App\DTOs\UntrustedHeaderDTO;
use Livewire\Attributes\Computed;
use App\Facades\Securities\HoneypotFacade;
use Illuminate\Support\Facades\RateLimiter;
use App\Facades\Securities\RateLimiterFacade;
use App\Livewire\Forms\Securities\RoadAccessForm;
use App\Livewire\Forms\Headers\UntrustedHeadersForm;
use App\Facades\Securities\OriginHeaderValidatorFacade;
use App\Enums\UntrustedHeaders\UntrustedHeaderNamesEnum;
use App\Facades\UntrustedHeaders\CreateUntrustedHeaderFacade;

class CreateUntrustedHeadersComponent extends Component
{

    public UntrustedHeadersForm $form;
    public RoadAccessForm $roadAccessForm;

    public $revealCreateUntrustedHeaderModal = false;

    public function openModalToCreateUntrustedHeader()
    {
        $this->resetErrorBag();
        $this->form->resetForm();
        $this->roadAccessForm->generateTrackingTime();
        if ($this->roadAccessForm->checkUserAttempts('CreateUntrustedHeader:' . request()->ip(), 6, 300)) {return;}
        $this->revealCreateUntrustedHeaderModal = true;
    }

    public function createUntrustedHeader()
    {
        $validated = $this->form->validate();
        if ($this->roadAccessForm->checkUserAttempts('CreateUntrustedHeader:' . request()->ip(), 6, 300)) {return;}

        OriginHeaderValidatorFacade::validate();
        HoneypotFacade::inspect($this->roadAccessForm);
        $dto = new UntrustedHeaderDTO(...$validated);

        try {
            CreateUntrustedHeaderFacade::execute($dto);
            $this->form->resetForm();
            $this->dispatch('untrustedHeaderCreated');
        } catch (\Throwable $th) {
            $this->dispatch('untrustedHeaderCreationFailed');
        }
    }

    #[Computed]
    public function headerNamesOptions()
    {
        return UntrustedHeaderNamesEnum::getHeaderTypeOptions();
    }


    public function render()
    {
        return view('livewire.backend.settings.system.headers.create-untrusted-headers-component');
    }
}
