<div>
    <x-action-buttons :id="$allowedExternalIp->id" viewAction="openModalToViewAllowedExternalIp" />

    <x-dialog-modal wire:model="revealViewAllowedExternalIpModal" :maxWidth="'xl'">
        <x-slot name="content">
            <x-divider nameClass="text-blue-400">
                {{ __('READ ABOUT ALLOWED EXTERNAL IP') }}
            </x-divider>

            <div class="text-slate-200 text-base font-semibold my-2">
                IP Address: {{ $allowedExternalIp->ip_address }}
            </div>
            <x-notice class="bg-green-600 text-amber-50 py-2 border mt-1 " noticeClass="uppercase text-xs font-bold">
                <x-slot:notice>Description:</x-slot>
                <div class="text-sm text-slate-100 my-2">
                    {{ $allowedExternalIp->description }}
                </div>
            </x-notice>

            <div class="sm:flex items-center mt-4">
                <x-button class=" bg-green-600 hover:bg-green-700" wire:click="$toggle('revealViewAllowedExternalIpModal')"
                    target="revealViewAllowedExternalIpModal" icon="thumbs-up">
                    {{ __('Done') }}
                </x-button>
            </div>
        </x-slot>

    </x-dialog-modal>
</div>
