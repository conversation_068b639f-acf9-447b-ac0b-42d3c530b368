<div>
    <x-action-buttons :id="$blockedIp->id" editAction="openModalToUpdateBlockedIp" />

    <x-dialog-modal wire:model="revealUpdateBlockedIpModal" :maxWidth="'xl'">
        <x-slot name="content">
            <x-divider nameClass="text-blue-400">
                {{ __('UPDATE BLOCKED IP ADDRESS') }}
            </x-divider>
        </x-slot>
        <x-slot name="content">
            <x-road-access />
            <x-rate-limiter-message :message="$this->roadAccessForm->road_access_rate_limiter" />

            @if (!$this->roadAccessForm->road_access_rate_limiter)
                <x-lable-input-error id="form.ip_address" type="text" label="IP address" model="form.ip_address" inputType="input"
                    value="{{ $form->ip_address }}" :onEnter="$form->getUpdateBlockedIpEvent()" placeholder="Enter blocked IP address...."
                    modelModifier="live.debounce.400ms" inputClass="w-full" icon="globe" iconClass="ps-2.5" />

                <x-lable-input-error id="form.reason" type="text" label="Reason" model="form.reason"
                    inputType="textarea" value="{{ $form->reason }}" :onEnter="$form->getUpdateBlockedIpEvent()"
                    placeholder="Enter reason for blocking this IP...." modelModifier="live.debounce.500ms"
                    inputClass="w-full" parentClass="mt-3" />
            @endif

            <div class="sm:flex items-center mt-4">
                <x-button class=" bg-red-500 hover:bg-red-600" wire:click="$toggle('revealUpdateBlockedIpModal')"
                    target="revealUpdateBlockedIpModal" icon='ban'>
                    {{ __('Cancel') }}
                </x-button>

                @if (!$this->roadAccessForm->road_access_rate_limiter)
                    <x-button class="ms-3  bg-green-500 hover:bg-green-600" wire:click="updateBlockedIp"
                        target="updateBlockedIp" :disabled="$form->hasErrors()" icon='cloud-arrow-up'>
                        {{ __('Update') }}
                    </x-button>
                @endif

                <x-action-message class="sm:ms-3 mt-2 sm:mt-0" on="blockedIpUpdated">
                    {{ __('Blocked IP address updated successfully.') }}
                </x-action-message>

                <x-action-message class="sm:ms-3 mt-2 sm:mt-0 bg-red-600 text-red-50" on="blockedIpUpdateFailed">
                    {{ __('Error occured while trying to update blocked IP address') }}
                </x-action-message>
            </div>
        </x-slot>

    </x-dialog-modal>

</div>
