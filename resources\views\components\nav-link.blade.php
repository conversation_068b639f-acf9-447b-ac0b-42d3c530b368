@props([
    'href' => '',
    'active' => false,
    'class' => '',
    'iconClass' => '',
    'icon' => 'question',
])

@php
    $isActive = $active ?: Route::currentRouteName() === $href;
    $url = $href ? route($href) : '#';
@endphp

<li class="list-none">
    <a wire:navigate.hover href="{{ $url }}" aria-current="{{ $isActive ? 'page' : 'false' }}"
        @class([
            'block px-1 py-1 rounded transition duration-200 text-slate-100',
            'bg-green-500 text-green-50 hover:text-green-500 md:bg-transparent md:text-green-500 border-b-2 border-green-500' => $isActive,
            'hover:text-blue-400 hover:border-blue-400' => !$isActive,
            $class,
        ])>
        @if ($icon)
            <div class="flex items-center">
                <span class="me-2 text-lg {{ $iconClass }}">
                    <i class="fa-solid fa-{{ $icon }}"></i>
                </span>
                <span>{{ $slot ?? 'Untitled' }}</span>
            </div>
        @else
            <span>{{ $slot ?? 'Untitled' }}</span>
        @endif
    </a>
</li>

