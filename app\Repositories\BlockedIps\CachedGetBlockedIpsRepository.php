<?php

namespace App\Repositories\BlockedIps;

use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Cache;
use App\Contracts\Repos\BlockedIpsRepos\GetBlockedIpsRepositoryInterface;

final class CachedGetBlockedIpsRepository implements GetBlockedIpsRepositoryInterface
{
    public function __construct(private GetBlockedIpsRepositoryInterface $getBlockedIpsRepository) {}

    public function handle(int $limit = 10, array $selectedColumns = ['*']): Collection
    {
        $fresh = 300; // 5 minutes
        $stale = 900; // 15 minutes

        $columns = $selectedColumns;
        sort($columns);
        $key = 'blocked_ips_list:' . $limit . ':' . implode(',', $columns);

        return Cache::flexible($key, [$fresh, $stale], function () use ($limit, $selectedColumns) {
            return $this->getBlockedIpsRepository->handle($limit, $selectedColumns);
        });
    }
}
