<?php

use Illuminate\Foundation\Application;
use App\Http\Middleware\validateIpAddresses;
use App\Http\Middleware\validateExternalIpAddresses;
use App\Http\Middleware\validateRequestHeaders;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        web: __DIR__ . '/../routes/web.php',
        commands: __DIR__ . '/../routes/console.php',
        health: '/up',
    )
    ->withMiddleware(function (Middleware $middleware): void {
        $middleware->web(append: [
            validateRequestHeaders::class,
        ]);

        $middleware->alias([
            'validateIpAddresses' => validateIpAddresses::class,
            'validateExternalIpAddresses' => validateExternalIpAddresses::class,
        ]);
    })
    ->withExceptions(function (Exceptions $exceptions): void {
        //
    })->create();
