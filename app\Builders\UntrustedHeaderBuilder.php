<?php

namespace App\Builders;

use Illuminate\Database\Eloquent\Builder;

class UntrustedHeaderBuilder extends Builder
{

    public function orderByIdAsc(): self
    {
        return $this->orderBy('id', 'asc');
    }
    public function orderByIdDesc(): self
    {
        return $this->orderBy('id', 'desc');
    }

    public function orderByNameAsc(): self
    {
        return $this->orderBy('header_type', 'asc');
    }
    public function orderByNameDesc(): self
    {
        return $this->orderBy('header_type', 'desc');
    }

    public function orderByPatternAsc(): self
    {
        return $this->orderBy('pattern', 'asc');
    }
    public function orderByPatternDesc(): self
    {
        return $this->orderBy('pattern', 'desc');
    }

    public function whereName(string $name): self
    {
        return $this->where('header_type', $name);
    }

    public function wherePattern(string $pattern): self
    {
        return $this->where('pattern', $pattern);
    }
}
