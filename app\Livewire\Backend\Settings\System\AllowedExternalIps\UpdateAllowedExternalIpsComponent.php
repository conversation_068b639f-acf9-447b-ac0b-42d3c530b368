<?php

namespace App\Livewire\Backend\Settings\System\AllowedExternalIps;

use Livewire\Component;
use App\Models\AllowedExternalIp;
use Livewire\Attributes\Locked;
use App\DTOs\AllowedExternalIpDTO;
use App\Facades\AllowedExternalIps\UpdateAllowedExternalIpFacade;
use App\Livewire\Forms\AllowedExternalIps\AllowedExternalIpsForm;
use App\Facades\Securities\OriginHeaderValidatorFacade;
use App\Facades\Securities\HoneypotFacade;
use App\Livewire\Forms\Securities\RoadAccessForm;

class UpdateAllowedExternalIpsComponent extends Component
{
    #[Locked]
    public AllowedExternalIp $allowedExternalIp;
    public AllowedExternalIpsForm $form;
    public RoadAccessForm $roadAccessForm;
    public $revealUpdateAllowedExternalIpModal = false;

    public function openModalToUpdateAllowedExternalIp()
    {
        $this->resetErrorBag();
        $this->roadAccessForm->generateTrackingTime();
        if ($this->roadAccessForm->checkUserAttempts('UpdateAllowedExternalIp:' . request()->ip(), 6, 300)) {return;}
        $this->form->selectedAllowedExternalIp = $this->allowedExternalIp;
        $this->form->ip_address = $this->allowedExternalIp->ip_address;
        $this->form->description = $this->allowedExternalIp->description;
        $this->revealUpdateAllowedExternalIpModal = true;
    }

    public function updateAllowedExternalIp()
    {
        $validated = $this->form->validate();
        if ($this->roadAccessForm->checkUserAttempts('UpdateAllowedExternalIp:' . request()->ip(), 6, 300)) {return;}

        OriginHeaderValidatorFacade::validate();
        HoneypotFacade::inspect($this->roadAccessForm);
        $dto = new AllowedExternalIpDTO(...$validated);

        try {
            UpdateAllowedExternalIpFacade::execute($this->allowedExternalIp, $dto);
            $this->dispatch('allowedExternalIpUpdated');
        } catch (\Throwable $th) {
            $this->dispatch('allowedExternalIpUpdateFailed');
        }
    }

    public function render()
    {
        return view('livewire.backend.settings.system.allowed-external-ips.update-allowed-external-ips-component');
    }
}
