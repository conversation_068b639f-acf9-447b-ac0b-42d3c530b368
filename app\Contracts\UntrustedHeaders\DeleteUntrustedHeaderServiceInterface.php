<?php

namespace App\Contracts\UntrustedHeaders;

use App\Models\UntrustedHeader;
use Illuminate\Container\Attributes\Bind;
use Illuminate\Container\Attributes\Singleton;
use App\Services\UntrustedHeaders\DeleteUntrustedHeaderService;

#[Bind(DeleteUntrustedHeaderService::class)]
interface DeleteUntrustedHeaderServiceInterface
{
    public function execute(UntrustedHeader $selectedUntrustedHeader): bool;
}
