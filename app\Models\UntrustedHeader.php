<?php

namespace App\Models;

use App\Casts\TransformStringCast;
use App\Traits\Defaults\HasFilterables;
use App\Traits\Defaults\HasSearchables;
use Illuminate\Database\Eloquent\Model;
use App\Builders\UntrustedHeaderBuilder;
use Illuminate\Contracts\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use App\Enums\UntrustedHeaders\UntrustedHeaderNamesEnum;

class UntrustedHeader extends Model
{
    /** @use HasFactory<\Database\Factories\UntrustedHeaderFactory> */
    use HasFactory;
    use HasSearchables;
    use HasFilterables;


    protected $fillable = [
        'header_type',
        'pattern',
        'description',
    ];

    protected $searchable = [
        'pattern',
        'description',
    ];

    protected $filterable = [
        'filterHeadersByName' => 'header_type',
    ];

    protected $casts = [
        'header_type' => UntrustedHeaderNamesEnum::class,
        'pattern' => TransformStringCast::class . ':capitalize',
        'description' => 'string',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    public function newEloquentBuilder($query): Builder
    {
        return new UntrustedHeaderBuilder($query);
    }


}
