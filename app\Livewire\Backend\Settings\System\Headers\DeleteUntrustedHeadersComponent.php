<?php

namespace App\Livewire\Backend\Settings\System\Headers;

use Livewire\Component;
use Livewire\Attributes\Locked;
use App\Livewire\Forms\Securities\RoadAccessForm;
use App\Facades\UntrustedHeaders\DeleteUntrustedHeaderFacade;

class DeleteUntrustedHeadersComponent extends Component
{

    #[Locked]
    public $header;
    public RoadAccessForm $roadAccessForm;
    public $revealDeleteUntrustedHeaderModal = false;

    public function openModalToDeleteUntrustedHeader()
    {
        $this->roadAccessForm->generateTrackingTime();
        $this->revealDeleteUntrustedHeaderModal = true;

    }

    public function deleteUntrustedHeader()
    {
        if ($this->roadAccessForm->checkUserAttempts('DeleteUntrustedHeader:' . request()->ip(), 6, 300)) {return;}
        try {
            DeleteUntrustedHeaderFacade::execute($this->header);
            $this->dispatch('untrustedHeaderDeleted');
            $this->revealDeleteUntrustedHeaderModal = false;
            $this->showSuccessSweetAlert('Deleted', 'Untrusted header deleted successfully');
        } catch (\Throwable $th) {
            $this->dispatch('untrustedHeaderDeleteFailed');
        }
    }


    public function render()
    {
        return view('livewire.backend.settings.system.headers.delete-untrusted-headers-component');
    }
}
