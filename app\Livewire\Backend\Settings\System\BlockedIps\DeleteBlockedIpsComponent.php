<?php

namespace App\Livewire\Backend\Settings\System\BlockedIps;

use Livewire\Component;
use Livewire\Attributes\Locked;
use App\Facades\BlockedIps\DeleteBlockedIpFacade;
use App\Models\BlockedIp;
use App\Livewire\Forms\Securities\RoadAccessForm;

class DeleteBlockedIpsComponent extends Component
{
    #[Locked]
    public BlockedIp $blockedIp;
    public RoadAccessForm $roadAccessForm;
    public $revealDeleteBlockedIpModal = false;

    public function openModalToDeleteBlockedIp()
    {
        $this->roadAccessForm->generateTrackingTime();
        if ($this->roadAccessForm->checkUserAttempts('DeleteBlockedIp:' . request()->ip(), 6, 300)) {return;}
        $this->revealDeleteBlockedIpModal = true;
    }

    public function deleteBlockedIp()
    {
        if ($this->roadAccessForm->checkUserAttempts('DeleteBlockedIp:' . request()->ip(), 6, 300)) {return;}
        try {
            DeleteBlockedIpFacade::execute($this->blockedIp);
            $this->dispatch('blockedIpDeleted');
            $this->showSuccessSweetAlert('Deleted', 'Blocked ip address deleted successfully');
            $this->revealDeleteBlockedIpModal = false;
        } catch (\Throwable $th) {
            $this->dispatch('blockedIpDeleteFailed');
        }
    }

    public function render()
    {
        return view('livewire.backend.settings.system.blocked-ips.delete-blocked-ips-component');
    }
}
