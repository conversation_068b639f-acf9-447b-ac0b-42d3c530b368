<?php

namespace App\Repositories\Roles;

use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Cache;
use App\Contracts\Repos\RolesRepos\GetRolesRepositoryInterface;

final class CachedGetRolesRepository implements GetRolesRepositoryInterface
{
    public function __construct(private GetRolesRepositoryInterface $getRolesRepository) {}

    public function handle(int $limit = 10, array $selectedColumns = ['*']): Collection
    {
        $fresh = 300; // 5 minutes
        $stale = 900; // 15 minutes

        $columns = $selectedColumns;
        sort($columns);
        $key = 'roles_list:' . $limit . ':' . implode(',', $columns);

        return Cache::flexible($key, [$fresh, $stale], function () use ($limit, $selectedColumns) {
            return $this->getRolesRepository->handle($limit, $selectedColumns);
        });
    }
}
