<?php

namespace App\Repositories\BlockedIps;

use Illuminate\Support\Facades\Cache;
use App\Contracts\Repos\BlockedIpsRepos\ForgetCachedGetBlockedIpByIdRepositoryInterface;

final class ForgetCachedGetBlockedIpByIdRepository implements ForgetCachedGetBlockedIpByIdRepositoryInterface
{
    public function handle(int $id, array $selectedColumns = ['*']): void
    {
        $columns = $selectedColumns;
        sort($columns); // normalize to avoid order mismatch
        $key = 'blocked_ip_id:' . $id . ':' . implode(',', $columns);
        Cache::forget($key);
    }
}
