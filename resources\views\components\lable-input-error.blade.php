@props([
    'id' => '', // Input ID
    'label' => '', // Label text
    'inputType' => 'text', // Type of field: 'input' or 'textarea'
    'model' => '', // Wire model name
    'value' => null, // Actual value of the model
    'modelModifier' => 'blur', // Default modifier for wire:model
    'placeholder' => '', // Placeholder text
    'parentClass' => '', // Additional classes for the parent div
    'labelClass' => '', // Additional classes for the label
    'inputClass' => '', // Additional classes for the input
    'errorClass' => '', // Additional classes for the error message
    'icon' => '', // Optional icon to display
    'iconClass' => '', // Additional classes for the icon
    'onEnter' => '', // Action for wire:keydown.enter
    'disabled' => false, // Input disabled state
])

<div class="mt-1  {{ $parentClass }}">

    @if ($label)
        <label for="{{ $id }}"
            class="block font-medium text-sm mb-0.5
            {{ $errors->has($model) ? 'text-red-500 italic' : ($value ? 'text-green-500' : 'text-slate-300') }}
            {{ $labelClass }}">
            {{ $label }}
        </label>
    @endif

    {{-- Input --}}
    <div class="relative">
        {{-- Optional Icon or Slot --}}
        @if ($icon && $inputType <> 'textarea')
            <div
                class="absolute inset-y-0 start-0 flex items-center pointer-events-none {{ $iconClass }}  {{ $errors->has($model) ? 'text-red-600' : ($value ? 'text-green-500' : 'text-blue-500') }}">
                <i class="fa-solid fa-{{ $icon }}"></i>
            </div>
        @endif

        {{-- Field --}}
        @if ($inputType === 'textarea')
            <textarea rows="4" id="{{ $id }}" placeholder="{{ $placeholder }}"
                @isset($model) wire:model{{ $modelModifier ? ".$modelModifier" : '' }}="{{ $model }}"@endisset
                @if ($onEnter) wire:keydown.enter="{{ $onEnter }}" @endif
                class="block p-2.5 w-full text-sm text-slate-900 bg-slate-50 rounded-md border border-slate-300  focus:ring-blue-500 focus:border-blue-500
                {{ $errors->has($model)
                    ? 'border-2 border-red-500 focus:ring-red-500 text-red-600 italic focus:outline-none'
                    : ($value
                        ? 'border-2  border-green-500 text-green-500 focus:ring-green-500 focus:outline-none'
                        : 'border-gray-600 focus:ring-gray-600') }}
                {{ $inputClass }}">
            </textarea>
        @else
            <input id="{{ $id }}" type="{{ $inputType }}" placeholder="{{ $placeholder }}"
                @isset($model) wire:model{{ $modelModifier ? ".$modelModifier" : '' }}="{{ $model }}"@endisset
                @if ($onEnter) wire:keydown.enter="{{ $onEnter }}" @endif
                {{ $disabled ? 'disabled' : '' }}
                class="ps-9 py-1 block shadow-sm  bg-slate-50 rounded
            {{ $errors->has($model)
                ? 'border-2 border-red-500 focus:ring-red-500 text-red-600 italic focus:outline-none'
                : ($value
                    ? 'border-2  border-green-500 text-green-500 focus:ring-green-500 focus:outline-none'
                    : 'border-gray-600 focus:ring-gray-600') }}
            {{ $inputClass }}"
                {{ $attributes }}>
        @endif

    </div>

    {{-- Error --}}
    @error($model)
        <span class="text-sm italic text-red-500 {{ $errorClass }}">{{ $message }}</span>
    @enderror
</div>
