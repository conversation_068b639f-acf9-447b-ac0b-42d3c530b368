<?php

namespace App\Enums\UntrustedHeaders;

enum UntrustedHeaderNamesEnum: int
{
    case USER_AGENT = 1;
    case REFERER = 2;
    case ACCEPT_LANGUAGE = 3;

    public function label(): string
    {
        return match ($this) {
            self::USER_AGENT => 'User-agent',
            self::REFERER => 'Referer',
            self::ACCEPT_LANGUAGE => 'Accept-language',
        };
    }

    public static function getHeaderTypeOptions(): array
    {
        $options = [];
        foreach (self::cases() as $case) {
            $options[$case->value] = $case->label();
        }
        return $options;
    }
}
