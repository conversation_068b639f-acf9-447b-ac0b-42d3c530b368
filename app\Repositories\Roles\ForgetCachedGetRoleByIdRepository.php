<?php

namespace App\Repositories\Roles;

use Illuminate\Support\Facades\Cache;
use App\Contracts\Repos\RolesRepos\ForgetCachedGetRoleByIdRepositoryInterface;

final class ForgetCachedGetRoleByIdRepository implements ForgetCachedGetRoleByIdRepositoryInterface
{
    public function handle(int $id, array $selectedColumns = ['*']): void
    {
        $columns = $selectedColumns;
        sort($columns); // normalize to avoid order mismatch
        $key = 'role_id:' . $id . ':' . implode(',', $columns);
        Cache::forget($key);
    }
}
