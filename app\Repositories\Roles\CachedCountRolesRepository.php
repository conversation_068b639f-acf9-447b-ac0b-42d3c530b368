<?php

namespace App\Repositories\Roles;

use Illuminate\Support\Facades\Cache;
use App\Contracts\Repos\RolesRepos\CountRolesRepositoryInterface;

final class CachedCountRolesRepository implements CountRolesRepositoryInterface
{

    public function __construct(private CountRolesRepositoryInterface $countRolesRepository) {}

    public function handle(): int
    {
        $key   = 'roles:count';
        $fresh = 300; // 5 minutes
        $stale = 900; // 15 minutes

        return Cache::flexible($key, [$fresh, $stale], function () {
            return $this->countRolesRepository->handle();
        });
    }
}
