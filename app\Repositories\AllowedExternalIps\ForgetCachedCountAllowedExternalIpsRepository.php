<?php

namespace App\Repositories\AllowedExternalIps;

use Illuminate\Support\Facades\Cache;
use App\Contracts\Repos\AllowedExternalIpsRepos\ForgetCachedCountAllowedExternalIpsRepositoryInterface;

class ForgetCachedCountAllowedExternalIpsRepository implements ForgetCachedCountAllowedExternalIpsRepositoryInterface
{
    public function handle(): void
    {
        $key = 'allowed_external_ips:count';
        Cache::forget($key);
    }
}
