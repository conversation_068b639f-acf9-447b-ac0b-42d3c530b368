<?php

namespace App\Contracts\UntrustedHeaders;

use App\Models\UntrustedHeader;
use App\DTOs\UntrustedHeaderDTO;
use Illuminate\Container\Attributes\Bind;
use Illuminate\Container\Attributes\Singleton;
use App\Services\UntrustedHeaders\UpdateUntrustedHeaderService;

#[Bind(UpdateUntrustedHeaderService::class)]
interface UpdateUntrustedHeaderServiceInterface
{
    public function execute(UntrustedHeader $selectedUntrustedHeader, UntrustedHeaderDTO $dto): UntrustedHeader;
}
