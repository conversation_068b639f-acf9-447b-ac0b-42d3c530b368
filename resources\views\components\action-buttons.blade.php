@props(['id', 'viewAction' => '', 'editAction' => '', 'deleteAction' => ''])

<div class="flex space-x-1">
    @if ($viewAction)
        <button wire:loading.attr="disabled" wire:click="{{ $viewAction }}"
            wire:loading.class='opacity-20 ' class="pr-1 font-medium text-yellow-700 cursor-pointer items-center">
            <i class="px-2 py-0.5 text-xs bg-yellow-200 rounded-full fa-solid fa-eye"></i>
        </button>
    @endif

    @if ($editAction)
        <button wire:loading.attr="disabled" wire:click="{{ $editAction }}"
            wire:loading.class='opacity-20' class="pr-1 font-medium text-green-600 cursor-pointer  items-center">
            <i class="px-2 py-0.5 text-xs bg-green-200 rounded-full fa-solid fa-pen-to-square"></i>
        </button>
    @endif

    @if ($deleteAction)
        <button wire:loading.attr="disabled" wire:click="{{ $deleteAction }}"
            wire:loading.class='opacity-20' class="font-medium text-red-600 cursor-pointer  items-center">
            <i class="px-2 py-0.5 text-xs bg-red-200 rounded-full fa-solid fa-trash"></i>
        </button>
    @endif
</div>
