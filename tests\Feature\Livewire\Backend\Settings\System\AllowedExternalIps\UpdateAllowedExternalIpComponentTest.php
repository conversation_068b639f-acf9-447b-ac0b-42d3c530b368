<?php

namespace Tests\Feature\Livewire\Backend\Settings\System\AllowedExternalIps;

use App\Livewire\Backend\Settings\System\AllowedExternalIps\UpdateAllowedExternalIpComponent;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Livewire\Livewire;
use Tests\TestCase;

class UpdateAllowedExternalIpComponentTest extends TestCase
{
    public function test_renders_successfully()
    {
        Livewire::test(UpdateAllowedExternalIpComponent::class)
            ->assertStatus(200);
    }
}
