<?php

namespace Tests\Feature\Livewire\Backend\Settings\System\Headers;

use App\Livewire\Backend\Settings\System\Headers\DeleteUntrustedHeadersComponent;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Livewire\Livewire;
use Tests\TestCase;

class DeleteUntrustedHeadersComponentTest extends TestCase
{
    public function test_renders_successfully()
    {
        Livewire::test(DeleteUntrustedHeadersComponent::class)
            ->assertStatus(200);
    }
}
