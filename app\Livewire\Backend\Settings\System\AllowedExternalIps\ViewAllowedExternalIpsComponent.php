<?php

namespace App\Livewire\Backend\Settings\System\AllowedExternalIps;

use Livewire\Component;
use Livewire\Attributes\Locked;
use App\Models\AllowedExternalIp;

class ViewAllowedExternalIpsComponent extends Component
{
    #[Locked]
    public AllowedExternalIp $allowedExternalIp;
    public $revealViewAllowedExternalIpModal = false;
    public function openModalToViewAllowedExternalIp()
    {
        $this->revealViewAllowedExternalIpModal = true;
    }

    public function render()
    {
        return view('livewire.backend.settings.system.allowed-external-ips.view-allowed-external-ips-component');
    }
}
