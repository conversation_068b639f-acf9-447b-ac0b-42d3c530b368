<?php

namespace App\Livewire\Forms\BlockedIps;

use Livewire\Form;
use App\Models\BlockedIp;
use App\Rules\GibberishRule;
use Livewire\Attributes\Validate;
use Illuminate\Validation\Rule;

class BlockedIpsForm extends Form
{
    #[Validate]
    public $ip_address;
    #[Validate]
    public $reason;

    public ?BlockedIp $selectedBlockedIp  = null;

    public function rules(): array
    {
        return [
            'ip_address' => array_filter([
                'required',
                'string',
                'min:3',
                'max:100',
                new GibberishRule('ip_address', 3, 3),
                Rule::unique('blocked_ips')->ignore($this->selectedBlockedIp)
            ]),
            'reason' => array_filter([
                'required',
                'string',
                'min:5',
                'max:500',
                new GibberishRule('reason', 3, 2)
            ]),
        ];
    }


    public function resetForm()
    {
        $this->reset(['ip_address', 'reason']);
    }

    public function hasErrors(): bool
    {
        return $this->getErrorBag()->isNotEmpty() || empty($this->ip_address) || empty($this->reason);
    }

    public function getCreateBlockedIpEvent(): string
    {
        return $this->hasErrors() ? '' : 'createBlockedIp';
    }

    public function getUpdateBlockedIpEvent(): string
    {
        return $this->hasErrors() ? '' : 'updateBlockedIp';
    }

}
