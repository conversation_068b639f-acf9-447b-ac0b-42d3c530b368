<?php

namespace App\Repositories\Roles;

use App\Models\Role;
use Illuminate\Support\Facades\Cache;
use App\Contracts\Repos\RolesRepos\GetRoleByIdRepositoryInterface;

final class CachedGetRoleByIdRepository implements GetRoleByIdRepositoryInterface
{
    public function __construct(private GetRoleByIdRepositoryInterface $getRoleByIdRepository) {}

    public function handle(int $id, array $selectedColumns = ['*']): ?Role
    {
        $fresh = 1800; // 30 minutes
        $stale = 3600; // 1 hour

        $columns = $selectedColumns;
        sort($columns); // normalize to avoid order mismatch
        $key = 'role_id:' . $id . ':' . implode(',', $columns);

        return Cache::flexible($key, [$fresh, $stale], function () use ($id, $selectedColumns) {
            return $this->getRoleByIdRepository->handle($id, $selectedColumns);
        });
    }
}
