<?php

namespace App\Facades\Repos\AllowedExternalIps;

use Illuminate\Support\Facades\Facade;
use App\Contracts\Repos\AllowedExternalIpsRepos\ForgetCachedGetAllowedExternalIpsRepositoryInterface;

class ForgetCachedGetAllowedExternalIpsFacade extends Facade
{
    protected static function getFacadeAccessor(): string
    {
        return ForgetCachedGetAllowedExternalIpsRepositoryInterface::class;
    }
}
