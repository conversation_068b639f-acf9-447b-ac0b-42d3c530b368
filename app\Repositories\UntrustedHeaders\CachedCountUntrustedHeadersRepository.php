<?php

namespace App\Repositories\UntrustedHeaders;

use Illuminate\Support\Facades\Cache;
use App\Contracts\Repos\UntrustedHeadersRepos\CountUntrustedHeadersRepositoryInterface;

final class CachedCountUntrustedHeadersRepository implements CountUntrustedHeadersRepositoryInterface
{

    public function __construct(private CountUntrustedHeadersRepositoryInterface $countUntrustedHeadersRepository) {}

    public function handle(): int
    {
        $key   = 'untrusted_headers:count';
        $fresh = 300; // 5 minutes
        $stale = 900; // 15 minutes

        return Cache::flexible($key, [$fresh, $stale], function () {
            return $this->countUntrustedHeadersRepository->handle();
        });
    }
}
