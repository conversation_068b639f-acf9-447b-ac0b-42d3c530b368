<?php

namespace App\Livewire\Backend\Settings\System\Headers;

use Livewire\Component;
use Livewire\Attributes\Locked;
use App\DTOs\UntrustedHeaderDTO;
use Livewire\Attributes\Computed;
use App\Facades\Securities\HoneypotFacade;
use App\Livewire\Forms\Securities\RoadAccessForm;
use App\Livewire\Forms\Headers\UntrustedHeadersForm;
use App\Facades\Securities\OriginHeaderValidatorFacade;
use App\Enums\UntrustedHeaders\UntrustedHeaderNamesEnum;
use App\Facades\UntrustedHeaders\UpdateUntrustedHeaderFacade;

class UpdateUntrustedHeadersComponent extends Component
{
    #[Locked]
    public $header;
    public UntrustedHeadersForm $headersForm;
    public RoadAccessForm $roadAccessForm;
    public $revealUpdateUntrustedHeaderModal = false;

    public function openModalToUpdateUntrustedHeader()
    {
        $this->resetErrorBag();
        $this->headersForm->resetForm();

        $this->headersForm->selectedUntrustedHeader = $this->header;
        $this->headersForm->header_type = $this->header->header_type->value;
        $this->headersForm->pattern = $this->header->pattern;
        $this->headersForm->description = $this->header->description;

        $this->roadAccessForm->generateTrackingTime();
        if ($this->roadAccessForm->checkUserAttempts('UpdateUntrustedHeader:' . request()->ip(), 6, 300)) {return;}
        $this->revealUpdateUntrustedHeaderModal = true;
    }


    public function updateUntrustedHeader()
    {
        $validated = $this->headersForm->validate();
        if ($this->roadAccessForm->checkUserAttempts('UpdateUntrustedHeader:' . request()->ip(), 6, 300)) {return;}

        OriginHeaderValidatorFacade::validate();
        HoneypotFacade::inspect($this->roadAccessForm);
        $dto = new UntrustedHeaderDTO(...$validated);

        try {
            UpdateUntrustedHeaderFacade::execute($this->header, $dto);
            $this->dispatch('untrustedHeaderUpdated');
        } catch (\Throwable $th) {
            $this->dispatch('untrustedHeaderUpdateFailed');
        }
    }

    #[Computed]
    public function headerNamesOptions()
    {
        return UntrustedHeaderNamesEnum::getHeaderTypeOptions();
    }

    public function render()
    {
        return view('livewire.backend.settings.system.headers.update-untrusted-headers-component');
    }
}
