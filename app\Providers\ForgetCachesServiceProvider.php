<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use App\Repositories\Roles\ForgetCachedGetRolesRepository;
use App\Repositories\Roles\ForgetCachedCountRolesRepository;
use App\Repositories\Roles\ForgetCachedGetRoleByIdRepository;
use App\Repositories\BlockedIps\ForgetCachedGetBlockedIpsRepository;
use App\Repositories\BlockedIps\ForgetCachedCountBlockedIpsRepository;
use App\Repositories\BlockedIps\ForgetCachedGetBlockedIpByIdRepository;
use App\Contracts\Repos\RolesRepos\ForgetCachedGetRolesRepositoryInterface;
use App\Contracts\Repos\RolesRepos\ForgetCachedCountRolesRepositoryInterface;
use App\Contracts\Repos\RolesRepos\ForgetCachedGetRoleByIdRepositoryInterface;
use App\Repositories\UntrustedHeaders\ForgetCachedGetUntrustedHeadersRepository;
use App\Repositories\UntrustedHeaders\ForgetCachedCountUntrustedHeadersRepository;
use App\Repositories\UntrustedHeaders\ForgetCachedGetUntrustedHeaderByIdRepository;
use App\Contracts\Repos\BlockedIpsRepos\ForgetCachedGetBlockedIpsRepositoryInterface;
use App\Contracts\Repos\BlockedIpsRepos\ForgetCachedCountBlockedIpsRepositoryInterface;
use App\Contracts\Repos\BlockedIpsRepos\ForgetCachedGetBlockedIpByIdRepositoryInterface;
use App\Contracts\Repos\UntrustedHeadersRepos\ForgetCachedGetUntrustedHeadersRepositoryInterface;
use App\Contracts\Repos\UntrustedHeadersRepos\ForgetCachedCountUntrustedHeadersRepositoryInterface;
use App\Contracts\Repos\UntrustedHeadersRepos\ForgetCachedGetUntrustedHeaderByIdRepositoryInterface;


class ForgetCachesServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        $this->app->bind(ForgetCachedGetRoleByIdRepositoryInterface::class, ForgetCachedGetRoleByIdRepository::class);
        $this->app->bind(ForgetCachedGetRolesRepositoryInterface::class, ForgetCachedGetRolesRepository::class);
        $this->app->bind(ForgetCachedCountRolesRepositoryInterface::class, ForgetCachedCountRolesRepository::class);
        $this->app->bind(ForgetCachedCountBlockedIpsRepositoryInterface::class, ForgetCachedCountBlockedIpsRepository::class);
        $this->app->bind(ForgetCachedGetBlockedIpsRepositoryInterface::class, ForgetCachedGetBlockedIpsRepository::class);
        $this->app->bind(ForgetCachedGetBlockedIpByIdRepositoryInterface::class, ForgetCachedGetBlockedIpByIdRepository::class);
        $this->app->bind(ForgetCachedCountUntrustedHeadersRepositoryInterface::class, ForgetCachedCountUntrustedHeadersRepository::class);
        $this->app->bind(ForgetCachedGetUntrustedHeadersRepositoryInterface::class, ForgetCachedGetUntrustedHeadersRepository::class);
        $this->app->bind(ForgetCachedGetUntrustedHeaderByIdRepositoryInterface::class, ForgetCachedGetUntrustedHeaderByIdRepository::class);
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        //
    }
}
