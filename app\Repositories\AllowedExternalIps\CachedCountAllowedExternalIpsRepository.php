<?php

namespace App\Repositories\AllowedExternalIps;

use Illuminate\Support\Facades\Cache;
use App\Contracts\Repos\AllowedExternalIpsRepos\CountAllowedExternalIpsRepositoryInterface;

class CachedCountAllowedExternalIpsRepository implements CountAllowedExternalIpsRepositoryInterface
{
    public function __construct(private CountAllowedExternalIpsRepositoryInterface $countAllowedExternalIpsRepository) {}

    public function handle(): int
    {
        $key   = 'allowed_external_ips:count';
        $fresh = 300; // 5 minutes
        $stale = 900; // 15 minutes

        return Cache::flexible($key, [$fresh, $stale], function () {
            return $this->countAllowedExternalIpsRepository->handle();
        });
    }
}
