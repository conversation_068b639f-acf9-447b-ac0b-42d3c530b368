<?php

namespace Tests\Feature\Livewire\Backend\Settings\System\Roles;

use App\Livewire\Backend\Settings\System\Roles\ShowRolesComponent;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Livewire\Livewire;
use Tests\TestCase;

class ShowRolesComponentTest extends TestCase
{
    public function test_renders_successfully()
    {
        Livewire::test(ShowRolesComponent::class)
            ->assertStatus(200);
    }
}
