<?php

namespace App\Models;

use App\Builders\BlockedIpBuilder;
use App\Traits\Defaults\HasFilterables;
use App\Traits\Defaults\HasSearchables;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class BlockedIp extends Model
{
    /** @use HasFactory<\Database\Factories\BlockedIpFactory> */
    use HasFactory;
    use HasSearchables;
    use HasFilterables;


    protected $fillable = [
        'ip_address',
        'reason',
    ];

    protected $searchable = [
        'ip_address',
        'reason',
    ];

    protected $casts = [
        'id' => 'integer',
        'ip_address' => 'string',
        'reason' => 'string',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    public function newEloquentBuilder($query): Builder
    {
        return new BlockedIpBuilder($query);
    }
}
