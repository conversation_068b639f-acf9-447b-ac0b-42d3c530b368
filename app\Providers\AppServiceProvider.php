<?php

namespace App\Providers;

use Illuminate\Support\Facades\Event;
use App\Events\Roles\RoleCreatedEvent;
use Illuminate\Support\ServiceProvider;
use App\Listeners\Roles\ClearGetRolesRepoCacheListener;



class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {

    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
         Event::listen(RoleCreatedEvent::class,ClearGetRolesRepoCacheListener::class);
    }
}
