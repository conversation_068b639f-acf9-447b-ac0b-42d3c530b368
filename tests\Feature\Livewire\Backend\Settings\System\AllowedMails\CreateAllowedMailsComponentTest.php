<?php

namespace Tests\Feature\Livewire\Backend\Settings\System\AllowedMails;

use App\Livewire\Backend\Settings\System\AllowedMails\CreateAllowedMailsComponent;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Livewire\Livewire;
use Tests\TestCase;

class CreateAllowedMailsComponentTest extends TestCase
{
    public function test_renders_successfully()
    {
        Livewire::test(CreateAllowedMailsComponent::class)
            ->assertStatus(200);
    }
}
