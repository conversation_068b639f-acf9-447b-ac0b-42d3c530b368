<?php

namespace App\Repositories\AllowedExternalIps;

use Illuminate\Support\Facades\Cache;
use App\Contracts\Repos\AllowedExternalIpsRepos\ForgetCachedGetAllowedExternalIpsRepositoryInterface;

class ForgetCachedGetAllowedExternalIpsRepository implements ForgetCachedGetAllowedExternalIpsRepositoryInterface
{
    public function handle(int $limit = 10, array $selectedColumns = ['*']): void
    {
        $columns = $selectedColumns;
        sort($columns); // normalize to avoid order mismatch
        $key = 'allowed_external_ips_list:' . $limit . ':' . implode(',', $columns);
        Cache::forget($key);
    }
}
