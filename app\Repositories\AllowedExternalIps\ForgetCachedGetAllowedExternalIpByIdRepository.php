<?php

namespace App\Repositories\AllowedExternalIps;

use Illuminate\Support\Facades\Cache;
use App\Contracts\Repos\AllowedExternalIpsRepos\ForgetCachedGetAllowedExternalIpByIdRepositoryInterface;

class ForgetCachedGetAllowedExternalIpByIdRepository implements ForgetCachedGetAllowedExternalIpByIdRepositoryInterface
{
    public function handle(int $id, array $selectedColumns = ['*']): void
    {
        $columns = $selectedColumns;
        sort($columns); // normalize to avoid order mismatch
        $key = 'allowed_external_ip_id:' . $id . ':' . implode(',', $columns);
        Cache::forget($key);
    }
}
