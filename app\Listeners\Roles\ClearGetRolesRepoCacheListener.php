<?php

namespace App\Listeners\Roles;

use App\Events\Roles\RoleCreatedEvent;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use App\Facades\Repos\Roles\ForgetCachedCountRolesFacade;
use App\Facades\Repos\Roles\ForgetCachedGetRolesFacade;

class ClearGetRolesRepoCacheListener
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(RoleCreatedEvent $event): void
    {
        ForgetCachedCountRolesFacade::handle();
        // ForgetCachedGetRolesFacade::handle();
    }
}
