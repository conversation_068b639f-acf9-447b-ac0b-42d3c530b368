<?php

namespace App\Livewire\Backend\Settings\System\Roles;

use App\Models\Role;
use App\DTOs\RoleDTO;
use Livewire\Component;
use Illuminate\Support\Str;
use Livewire\Attributes\Locked;
use App\Enums\Roles\RoleTypeEnum;
use Livewire\Attributes\Computed;
use App\Enums\Roles\RoleTypesEnum;
use App\Facades\Roles\UpdateRoleFacade;
use App\Livewire\Forms\Roles\RolesForm;
use App\Facades\Securities\HoneypotFacade;
use App\Facades\Repos\Roles\GetRoleByIdFacade;
use App\Livewire\Forms\Securities\RoadAccessForm;
use App\Facades\Securities\OriginHeaderValidatorFacade;


class UpdateRolesComponent extends Component
{
    #[Locked]
    public Role $role;

    public RolesForm $form;
    public RoadAccessForm $roadAccessForm;

    public $revealUpdateRoleModal = false;

    public function openModalToUpdateRole()
    {
        $this->resetErrorBag();
        $this->form->resetForm();

        $this->form->selectedRole = $this->role;
        $this->form->name = $this->role->name;
        $this->form->type = $this->role->type->value;
        $this->form->description = $this->role->description;

        $this->roadAccessForm->generateTrackingTime();
        if ($this->roadAccessForm->checkUserAttempts('UpdateRole:' . request()->ip(), 6, 300)) {return;}
        $this->revealUpdateRoleModal = true;
    }


    public function updateRole()
    {
        $validated = $this->form->validate();
        if ($this->roadAccessForm->checkUserAttempts('UpdateRole:' . request()->ip(), 6, 300)) {return;}

        OriginHeaderValidatorFacade::validate();
        HoneypotFacade::inspect($this->roadAccessForm);
        $validated['slug'] = Str::slug($validated['name']);
        $dto = new RoleDTO(...$validated);

        try {
            UpdateRoleFacade::execute($this->role, $dto);
            $this->dispatch('roleUpdated');
        } catch (\Throwable $th) {
            $this->dispatch('roleUpdateFailed');
        }
    }

    #[Computed]
    public function roleTypeOptions()
    {
        return RoleTypesEnum::getRoleTypeOptions();
    }

    public function render()
    {
        return view('livewire.backend.settings.system.roles.update-roles-component');
    }
}
