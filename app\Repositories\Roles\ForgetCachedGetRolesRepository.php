<?php

namespace App\Repositories\Roles;

use Illuminate\Support\Facades\Cache;
use App\Contracts\Repos\RolesRepos\ForgetCachedGetRolesRepositoryInterface;

final class ForgetCachedGetRolesRepository implements ForgetCachedGetRolesRepositoryInterface
{
    public function handle(int $limit = 10, array $selectedColumns = ['*']): void
    {
        $columns = $selectedColumns;
        sort($columns);
        $key = 'roles_list:' . $limit . ':' . implode(',', $columns);
        Cache::forget($key);
    }
}
