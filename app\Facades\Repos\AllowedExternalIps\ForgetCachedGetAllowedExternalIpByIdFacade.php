<?php

namespace App\Facades\Repos\AllowedExternalIps;

use Illuminate\Support\Facades\Facade;
use App\Contracts\Repos\AllowedExternalIpsRepos\ForgetCachedGetAllowedExternalIpByIdRepositoryInterface;

class ForgetCachedGetAllowedExternalIpByIdFacade extends Facade
{
    protected static function getFacadeAccessor(): string
    {
        return ForgetCachedGetAllowedExternalIpByIdRepositoryInterface::class;
    }
}
