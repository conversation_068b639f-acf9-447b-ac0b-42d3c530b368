<?php

namespace App\DTOs;

use App\Enums\AllowedMails\AllowedMailStatusEnum;

class AllowedMailDTO
{
    public function __construct(
        public readonly ?string $email = null,
        public readonly int $status = AllowedMailStatusEnum::AUTHORISED->value,
        public readonly ?string $description = null,

    ) {}

    public function toArray(): array
    {
        return array_filter([
            'email' => $this->email,
            'status' => $this->status,
            'description' => $this->description,
        ], fn($value) => !is_null($value));
    }
}
