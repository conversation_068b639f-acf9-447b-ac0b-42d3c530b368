<?php

namespace Tests\Feature\Livewire\Backend\Settings\System;

use App\Livewire\Backend\Settings\System\SystemSettingsComponent;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Livewire\Livewire;
use Tests\TestCase;

class SystemSettingsComponentTest extends TestCase
{
    public function test_renders_successfully()
    {
        Livewire::test(SystemSettingsComponent::class)
            ->assertStatus(200);
    }
}
