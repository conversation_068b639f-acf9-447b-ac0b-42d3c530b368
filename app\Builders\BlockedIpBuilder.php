<?php

namespace App\Builders;

use Illuminate\Database\Eloquent\Builder;

class BlockedIpBuilder extends Builder
{
    public function orderByIdAsc(): self
    {
        return $this->orderBy('id', 'asc');
    }
    public function orderByIdDesc(): self
    {
        return $this->orderBy('id', 'desc');
    }

    public function orderByIpAsc(): self
    {
        return $this->orderBy('ip_address', 'asc');
    }
    public function orderByIpDesc(): self
    {
        return $this->orderBy('ip_address', 'desc');
    }

    public function whereIp(string $ip): self
    {
        return $this->where('ip_address', $ip);
    }


}
