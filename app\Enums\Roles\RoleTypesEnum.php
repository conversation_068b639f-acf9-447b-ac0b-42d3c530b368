<?php

namespace App\Enums\Roles;

enum RoleTypesEnum: int
{
    case PLATFORM = 1;
    case ORGANISATION = 2;

    public function label(): string
    {
        return match ($this) {
            self::PLATFORM => 'Platform',
            self::ORGANISATION => 'Organisation',
        };
    }

    public static function getRoleTypeOptions(): array
    {
        $options = [];

        foreach (self::cases() as $case) {
            $options[$case->value] = $case->label();
        }

        return $options;
    }
}
