<?php

namespace App\Services\Securities;

use App\Contracts\Securities\AllowAuthorisedIpsServiceInterface;
use App\Facades\Repos\AllowedExternalIps\GetAllowedExternalIpsFacade;

class AllowAuthorisedIpsService implements AllowAuthorisedIpsServiceInterface
{
    public function allowAuthorised(): bool
    {
        $allowedIps = GetAllowedExternalIpsFacade::handle(100000, ['ip_address'])->pluck('ip_address')->toArray();

        if (in_array(request()->ip(), $allowedIps, true)) {
            return true;
        }
        return false;
    }
}
