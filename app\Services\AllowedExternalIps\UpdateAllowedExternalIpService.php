<?php

namespace App\Services\AllowedExternalIps;

use App\Models\AllowedExternalIp;
use App\DTOs\AllowedExternalIpDTO;
use App\Actions\AllowedExternalIps\UpdateAllowedExternalIpAction;
use App\Contracts\AllowedExternalIps\UpdateAllowedExternalIpServiceInterface;

class UpdateAllowedExternalIpService implements UpdateAllowedExternalIpServiceInterface
{
    public function execute(AllowedExternalIp $selectedAllowedExternalIp, AllowedExternalIpDTO $dto): AllowedExternalIp
    {
        return UpdateAllowedExternalIpAction::handle($selectedAllowedExternalIp, $dto->toArray());
    }
}
