<?php

namespace App\Traits\Defaults;

use Illuminate\Database\Eloquent\Builder;
use Exception;
use Illuminate\Support\Str;

trait HasSearchables
{
     public function scopeSearch(Builder $builder, string $term = '')
    {
        $term = trim($term);

        if ($term === '') {
            return $builder;
        }

        if (!property_exists($this, 'searchable') || !is_array($this->searchable) || empty($this->searchable)) {
            throw new Exception('Please define the $searchable property as a non-empty array.');
        }

        $isNumeric = is_numeric($term);

        $builder->where(function ($query) use ($term, $isNumeric) {
            foreach ($this->searchable as $path) {
                if (str_contains($path, '.')) {
                    $relation = Str::beforeLast($path, '.');
                    $column   = Str::afterLast($path, '.');

                    $query->orWhereHas($relation, function ($q) use ($column, $term, $isNumeric) {
                        $q->where(function ($inner) use ($column, $term, $isNumeric) {
                            if ($isNumeric) {
                                $inner->orWhere($column, '=', $term);
                            }
                            $inner->orWhere($column, 'ILIKE', "%{$term}%");
                        });
                    });
                } else {
                    $query->orWhere(function ($inner) use ($path, $term, $isNumeric) {
                        if ($isNumeric) {
                            $inner->orWhere($path, '=', $term);
                        }
                        $inner->orWhere($path, 'ILIKE', "%{$term}%");
                    });
                }
            }
        });

        return $builder;
    }

}
