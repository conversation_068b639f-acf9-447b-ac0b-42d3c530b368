<?php

namespace App\Repositories\AllowedExternalIps;

use App\Models\AllowedExternalIp;
use Illuminate\Support\Facades\Cache;
use App\Contracts\Repos\AllowedExternalIpsRepos\GetAllowedExternalIpByIdRepositoryInterface;

class CachedGetAllowedExternalIpByIdRepository implements GetAllowedExternalIpByIdRepositoryInterface
{
    public function __construct(private GetAllowedExternalIpByIdRepositoryInterface $getAllowedExternalIpByIdRepository) {}

    public function handle(int $id, array $selectedColumns = ['*']): ?AllowedExternalIp
    {
        $fresh = 1800; // 30 minutes
        $stale = 3600; // 1 hour

        $columns = $selectedColumns;
        sort($columns); // normalize to avoid order mismatch
        $key = 'allowed_external_ip_id:' . $id . ':' . implode(',', $columns);

        return Cache::flexible($key, [$fresh, $stale], function () use ($id, $selectedColumns) {
            return $this->getAllowedExternalIpByIdRepository->handle($id, $selectedColumns);
        });
    }
}
