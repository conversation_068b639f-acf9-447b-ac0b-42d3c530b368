<?php

namespace App\Repositories\BlockedIps;

use Illuminate\Support\Facades\Cache;
use App\Contracts\Repos\BlockedIpsRepos\ForgetCachedGetBlockedIpsRepositoryInterface;

final class ForgetCachedGetBlockedIpsRepository implements ForgetCachedGetBlockedIpsRepositoryInterface
{
    public function handle(int $limit = 10, array $selectedColumns = ['*']): void
    {
        $columns = $selectedColumns;
        sort($columns); // normalize to avoid order mismatch
        $key = 'blocked_ips_list:' . $limit . ':' . implode(',', $columns);
        Cache::forget($key);
    }
}
