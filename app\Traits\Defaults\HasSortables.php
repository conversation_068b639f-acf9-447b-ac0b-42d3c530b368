<?php

namespace App\Traits\Defaults;

use Illuminate\Database\Eloquent\Builder;

trait HasSortables
{
    public function sortBy($sortMethodName, $baseMethod)
    {
        $ascending = $baseMethod . 'Asc';
        $descending = $baseMethod . 'Desc';

        if ($sortMethodName === $ascending) {
            return $sortMethodName = $descending;
        } elseif ($sortMethodName === $descending) {
            return $sortMethodName = null;
        } else {
            return $sortMethodName = $ascending;
        }
    }

    public function applySorting(Builder $query, ?string $sortMethod): Builder
    {
        if (method_exists($query, $sortMethod) && ($sortMethod <> 'orderByIdAsc' || $sortMethod !== 'orderByIdDesc')) {
            return $query->{$sortMethod}();
        }

        return $query->orderByIdDesc();
    }
}
