<?php

namespace App\Repositories\BlockedIps;

use App\Models\BlockedIp;
use Illuminate\Support\Collection;
use App\Contracts\Repos\BlockedIpsRepos\GetBlockedIpsRepositoryInterface;

final class GetBlockedIpsRepository implements GetBlockedIpsRepositoryInterface
{
    public function handle(int $limit = 10, array $selectedColumns = ['*']): Collection
    {
        return BlockedIp::select($selectedColumns)
            ->orderByIdDesc()
            ->limit($limit)
            ->get();
    }
}
