<?php

namespace App\Repositories\BlockedIps;

use App\Models\BlockedIp;
use Illuminate\Support\Facades\Cache;
use App\Contracts\Repos\BlockedIpsRepos\GetBlockedIpByIdRepositoryInterface;

final class CachedGetBlockedIpByIdRepository implements GetBlockedIpByIdRepositoryInterface
{
    public function __construct(private GetBlockedIpByIdRepositoryInterface $getBlockedIpByIdRepository) {}

    public function handle(int $id, array $selectedColumns = ['*']): ?BlockedIp
    {
        $fresh = 1800; // 30 minutes
        $stale = 3600; // 1 hour

        $columns = $selectedColumns;
        sort($columns); // normalize to avoid order mismatch
        $key = 'blocked_ip_id:' . $id . ':' . implode(',', $columns);

        return Cache::flexible($key, [$fresh, $stale], function () use ($id, $selectedColumns) {
            return $this->getBlockedIpByIdRepository->handle($id, $selectedColumns);
        });
    }
}
