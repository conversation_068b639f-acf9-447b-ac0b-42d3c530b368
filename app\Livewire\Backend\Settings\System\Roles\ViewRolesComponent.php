<?php

namespace App\Livewire\Backend\Settings\System\Roles;

use App\Models\Role;
use Livewire\Component;
use Livewire\Attributes\Locked;

class ViewRolesComponent extends Component
{
    #[Locked]
    public Role $role;
    public $revealViewRoleModal = false;


    public function openModalToViewRole()
    {
        $this->revealViewRoleModal = true;
    }


    public function render()
    {

        return view('livewire.backend.settings.system.roles.view-roles-component');
    }
}
