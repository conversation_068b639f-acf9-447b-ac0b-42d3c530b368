<?php

namespace App\Services\AllowedExternalIps;

use App\Models\AllowedExternalIp;
use App\DTOs\AllowedExternalIpDTO;
use App\Actions\AllowedExternalIps\CreateAllowedExternalIpAction;
use App\Contracts\AllowedExternalIps\CreateAllowedExternalIpServiceInterface;

class CreateAllowedExternalIpService implements CreateAllowedExternalIpServiceInterface
{
    public function execute(AllowedExternalIpDTO $dto): AllowedExternalIp
    {
        return CreateAllowedExternalIpAction::handle($dto->toArray());
    }
}
