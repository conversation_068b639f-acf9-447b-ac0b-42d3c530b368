<?php

namespace App\Builders;

use Illuminate\Database\Eloquent\Builder;

class AllowedExternalIpBuilder extends Builder
{
    public function orderByIdAsc(): self
    {
        return $this->orderBy('id', 'asc');
    }
    public function orderByIdDesc(): self
    {
        return $this->orderBy('id', 'desc');
    }

    public function orderByIpAddressAsc(): self
    {
        return $this->orderBy('ip_address', 'asc');
    }
    public function orderByIpAddressDesc(): self
    {
        return $this->orderBy('ip_address', 'desc');
    }

    public function whereIp(string $ip): self
    {
        return $this->where('ip_address', $ip);
    }
}
