<?php

namespace App\Services\Securities;

use App\Facades\Repos\BlockedIps\GetBlockedIpsFacade;
use App\Contracts\Securities\StopUnauthorisedIpsServiceInterface;

class StopUnauthorisedIpsService implements StopUnauthorisedIpsServiceInterface
{
    public function preventUnauthorized(): bool
    {
        $blockedIps = GetBlockedIpsFacade::handle(100000, ['ip_address'])->pluck('ip_address')->toArray();
        if (in_array(request()->ip(), $blockedIps, true)) {
            return true;
        }
        return false;
    }
}
