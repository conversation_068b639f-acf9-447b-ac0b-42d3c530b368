<?php

namespace App\Services\UntrustedHeaders;

use App\Models\UntrustedHeader;
use App\DTOs\UntrustedHeaderDTO;
use App\Actions\UntrustedHeaders\CreateUntrustedHeaderAction;
use App\Contracts\UntrustedHeaders\CreateUntrustedHeaderServiceInterface;

class CreateUntrustedHeaderService implements CreateUntrustedHeaderServiceInterface
{

    public function execute(UntrustedHeaderDTO $dto): UntrustedHeader
    {
        return CreateUntrustedHeaderAction::handle($dto->toArray());
    }
}
