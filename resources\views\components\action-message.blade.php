@props(['on'])

<div
    x-data="{ shown: false, timeout: null }"
    x-init="
        $wire.on('{{ $on }}', () => {
            clearTimeout(timeout);
            shown = true;
            timeout = setTimeout(() => { shown = false }, 2500);
        })
    "
    x-show.transition.out.opacity.duration.1500ms="shown"
    x-transition:leave.opacity.duration.1500ms
    x-cloak
    {{ $attributes->merge(['class' => 'text-sm text-green-600 bg-green-200 rounded px-4 py-1']) }}
    style="display: none;"
>
    {{ $slot->isEmpty() ? 'Saved.' : $slot }}
</div>
