<?php

namespace App\Repositories\UntrustedHeaders;

use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Cache;
use App\Contracts\Repos\UntrustedHeadersRepos\GetUntrustedHeadersRepositoryInterface;

final class CachedGetUntrustedHeadersRepository implements GetUntrustedHeadersRepositoryInterface
{
    public function __construct(private GetUntrustedHeadersRepositoryInterface $getUntrustedHeadersRepository) {}

    public function handle(int $limit = 10, array $selectedColumns = ['*']): Collection
    {
        $fresh = 300; // 5 minutes
        $stale = 900; // 15 minutes

        $columns = $selectedColumns;
        sort($columns);
        $key = 'untrusted_headers_list:' . $limit . ':' . implode(',', $columns);

        return Cache::flexible($key, [$fresh, $stale], function () use ($limit, $selectedColumns) {
            return $this->getUntrustedHeadersRepository->handle($limit, $selectedColumns);
        });
    }
}
