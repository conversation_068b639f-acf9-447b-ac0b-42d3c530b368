<?php

namespace App\Repositories\UntrustedHeaders;

use Illuminate\Support\Facades\Cache;
use App\Contracts\Repos\UntrustedHeadersRepos\ForgetCachedCountUntrustedHeadersRepositoryInterface;

final class ForgetCachedCountUntrustedHeadersRepository implements ForgetCachedCountUntrustedHeadersRepositoryInterface
{
    public function handle(): void
    {
        $key = 'untrusted-headers:count';
        Cache::forget($key);
    }
}
