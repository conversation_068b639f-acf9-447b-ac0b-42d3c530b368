<?php

namespace App\Repositories\UntrustedHeaders;

use App\Models\UntrustedHeader;
use Illuminate\Support\Collection;
use App\Contracts\Repos\UntrustedHeadersRepos\GetUntrustedHeadersRepositoryInterface;

final class GetUntrustedHeadersRepository implements GetUntrustedHeadersRepositoryInterface
{
    public function handle(int $limit = 10, array $selectedColumns = ['*']): Collection
    {
        return UntrustedHeader::select($selectedColumns)
            ->orderByIdDesc()
            ->limit($limit)
            ->get();
    }
}
