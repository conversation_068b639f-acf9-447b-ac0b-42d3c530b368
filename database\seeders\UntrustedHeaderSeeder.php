<?php

namespace Database\Seeders;

use App\Models\UntrustedHeader;
use Illuminate\Database\Seeder;
use App\Enums\UntrustedHeaders\UntrustedHeaderNamesEnum;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;

class UntrustedHeaderSeeder extends Seeder
{
    public function run(): void
    {
        $untrustedHeaders = [
            // ---------------------
            // User-Agent patterns
            // ---------------------
            [
                'header_type' => UntrustedHeaderNamesEnum::USER_AGENT->value,
                'pattern' => 'bot',
                'description' => 'This entry targets User-Agent headers containing the word "bot".
                Such requests are often generated by automated programs that attempt to crawl or index the website without permission.
                Blocking these prevents unwanted scraping, protects server resources, and reduces exposure to malicious reconnaissance.',
            ],
            [
                'header_type' => UntrustedHeaderNamesEnum::USER_AGENT->value,
                'pattern' => 'spider',
                'description' => 'User-Agents containing "spider" usually indicate web crawlers or indexing tools.
                While some spiders are legitimate, many are aggressive or malicious, scraping content or scanning for vulnerabilities.
                This rule helps reduce unauthorized crawling and potential attacks.',
            ],
            [
                'header_type' => UntrustedHeaderNamesEnum::USER_AGENT->value,
                'pattern' => 'crawl',
                'description' => 'This rule blocks requests with "crawl" in the User-Agent header.
                Crawlers typically harvest website data automatically, and malicious crawlers can overload servers, copy sensitive content, or probe for weaknesses.
                Blocking them enhances both performance and security.',
            ],
            [
                'header_type' => UntrustedHeaderNamesEnum::USER_AGENT->value,
                'pattern' => 'python-requests',
                'description' => 'The "python-requests" User-Agent is a common signature of Python scripts using the Requests library.
                While often used for testing, attackers also leverage it for automated scraping, brute-force attacks, or API abuse.
                Blocking this deters basic script-based exploits.',
            ],
            [
                'header_type' => UntrustedHeaderNamesEnum::USER_AGENT->value,
                'pattern' => 'curl',
                'description' => 'The cURL tool is frequently used to automate HTTP requests from scripts or command-line operations.
                Although useful for developers, attackers exploit it to probe websites, scrape content, and perform automated exploitation attempts.
                Blocking suspicious cURL traffic reduces the attack surface.',
            ],
            [
                'header_type' => UntrustedHeaderNamesEnum::USER_AGENT->value,
                'pattern' => 'wget',
                'description' => 'Wget is another command-line utility commonly used for downloading files and mirroring websites.
                Attackers often use it for automated scraping or to pull data during malicious activities.
                Blocking or monitoring Wget traffic helps prevent unauthorized data extraction.',
            ],
            [
                'header_type' => UntrustedHeaderNamesEnum::USER_AGENT->value,
                'pattern' => 'libwww-perl',
                'description' => 'This User-Agent is generated by scripts using the Perl LWP library.
                It is a known signature for older web scraping tools and automated exploits.
                Blocking it reduces exposure to poorly coded or malicious scripts that attempt to interact with your site automatically.',
            ],
            [
                'header_type' => UntrustedHeaderNamesEnum::USER_AGENT->value,
                'pattern' => 'nikto',
                'description' => 'Nikto is an open-source web server scanner widely used by penetration testers and attackers to discover vulnerabilities.
                Its User-Agent string is easily identifiable, and blocking it helps prevent reconnaissance from unauthorized parties.',
            ],

            // ---------------------
            // Referer patterns
            // ---------------------
            [
                'header_type' => UntrustedHeaderNamesEnum::REFERER->value,
                'pattern' => 'suspicious.com',
                'description' => 'Traffic with this referer is considered suspicious because it originates from a domain associated with spam or malicious activities.
                Such requests may attempt phishing, spam campaigns, or injection attacks, so flagging them as untrusted reduces exposure.',
            ],
            [
                'header_type' => UntrustedHeaderNamesEnum::REFERER->value,
                'pattern' => 'malicious-site',
                'description' => 'Requests that reference this domain are considered dangerous because it is known for hosting malware or orchestrating attacks.
                Blocking them helps prevent data leakage, protects user sessions, and adds an additional layer of security to the application.',
            ],
            [
                'header_type' => UntrustedHeaderNamesEnum::REFERER->value,
                'pattern' => 'fakebank-login',
                'description' => 'This referer pattern is commonly used in phishing attempts, especially fake banking or login pages that redirect unsuspecting
                users to the target site. Tracking and blocking such referers helps protect users from fraud and account compromise.',
            ],
            [
                'header_type' => UntrustedHeaderNamesEnum::REFERER->value,
                'pattern' => 'blackhatseo',
                'description' => 'This referer indicates black-hat SEO link injections or spam campaigns trying to exploit your website for backlinks.
                Blocking and monitoring these referers helps maintain site reputation and prevents SEO poisoning attempts.',
            ],

            // ---------------------
            // Accept-Language patterns
            // ---------------------
            [
                'header_type' => UntrustedHeaderNamesEnum::ACCEPT_LANGUAGE->value,
                'pattern' => 'zh-CN',
                'description' => 'While "zh-CN" is a valid language code for Simplified Chinese, traffic using this header may be suspicious
                if your application does not serve a Chinese audience. It is often observed in malicious scanners and automated attacks targeting global websites.',
            ],
            [
                'header_type' => UntrustedHeaderNamesEnum::ACCEPT_LANGUAGE->value,
                'pattern' => 'xx-YY',
                'description' => 'This rule targets invalid or non-standard language codes. Such malformed headers are commonly generated by bots,
                testing tools, or poorly configured scripts. Filtering them out helps detect and block non-human or potentially harmful traffic early.',
            ],
            [
                'header_type' => UntrustedHeaderNamesEnum::ACCEPT_LANGUAGE->value,
                'pattern' => 'zz-ZZ',
                'description' => 'This is another example of an invalid language code that typically does not appear in legitimate browser requests.
                It serves as a strong indicator of non-browser automated activity or malformed clients.',
            ],
        ];

        $insertData = [];
        foreach ($untrustedHeaders as $header) {
            $insertData[] = [
                'header_type' => $header['header_type'],
                'pattern' => $header['pattern'],
                'description' => $header['description'],
                'created_at' => now(),
                'updated_at' => now(),
            ];
        }

        UntrustedHeader::insert($insertData);
    }
}
