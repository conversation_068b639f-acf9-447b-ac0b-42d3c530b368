<?php

namespace App\DTOs;

class AllowedExternalIpDTO
{
    public function __construct(
        public readonly ?string $ip_address = null,
        public readonly ?string $description = null,

    ) {}

    public function toArray(): array
    {
        return array_filter([
            'ip_address' => $this->ip_address,
            'description' => $this->description,
        ], fn($value) => !is_null($value));
    }
}
