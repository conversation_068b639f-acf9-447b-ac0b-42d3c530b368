<?php

namespace App\Models;


use App\Traits\Defaults\HasFilterables;
use App\Traits\Defaults\HasSearchables;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Builder;
use App\Builders\AllowedExternalIpBuilder;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class AllowedExternalIp extends Model
{
    /** @use HasFactory<\Database\Factories\AllowedExternalIpFactory> */
    use HasFactory;
    use HasSearchables;
    use HasFilterables;

    protected $fillable = [
        'ip_address',
        'description',
    ];

    protected $searchable = [
        'ip_address',
        'description',
    ];


    protected $casts = [
        'ip_address' => 'string',
        'description' => 'string',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    public function newEloquentBuilder($query): Builder
    {
        return new AllowedExternalIpBuilder($query);
    }


}
