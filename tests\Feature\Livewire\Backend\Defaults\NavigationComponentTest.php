<?php

namespace Tests\Feature\Livewire\Backend\Defaults;

use App\Livewire\Backend\Defaults\NavigationComponent;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Livewire\Livewire;
use Tests\TestCase;

class NavigationComponentTest extends TestCase
{
    public function test_renders_successfully()
    {
        Livewire::test(NavigationComponent::class)
            ->assertStatus(200);
    }
}
