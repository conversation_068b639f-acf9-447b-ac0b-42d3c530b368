<?php

namespace App\Livewire\Backend\Settings\System\Headers;

use Livewire\Component;
use Livewire\Attributes\Locked;
use App\Livewire\Forms\Securities\RoadAccessForm;

class ViewUntrustedHeadersComponent extends Component
{
    #[Locked]
    public $header;
    public RoadAccessForm $roadAccessForm;
    public $revealViewUntrustedHeaderModal = false;

    public function openModalToViewUntrustedHeader()
    {
        $this->revealViewUntrustedHeaderModal = true;
    }
    public function render()
    {
        return view('livewire.backend.settings.system.headers.view-untrusted-headers-component');
    }
}
