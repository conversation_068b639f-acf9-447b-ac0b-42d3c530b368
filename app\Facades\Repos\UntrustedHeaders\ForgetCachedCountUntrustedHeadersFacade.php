<?php

namespace App\Facades\Repos\UntrustedHeaders;

use Illuminate\Support\Facades\Facade;
use App\Contracts\Repos\UntrustedHeadersRepos\ForgetCachedCountUntrustedHeadersRepositoryInterface;

class ForgetCachedCountUntrustedHeadersFacade extends Facade
{
    protected static function getFacadeAccessor(): string
    {
        return ForgetCachedCountUntrustedHeadersRepositoryInterface::class;
    }
}
