<?php

namespace App\Repositories\Roles;

use App\Models\Role;
use Illuminate\Support\Collection;
use App\Contracts\Repos\RolesRepos\GetRolesRepositoryInterface;

final class GetRolesRepository implements GetRolesRepositoryInterface
{
    public function handle(int $limit = 10, array $selectedColumns = ['*']): Collection
    {
        return Role::select($selectedColumns)
            ->orderByIdDesc()
            ->limit($limit)
            ->get();
    }
}
