<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use App\Facades\Securities\SuspiciousHeadersFacade;
use App\Facades\Securities\StopUnauthorisedIpsFacade;

class validateRequestHeaders
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {

           SuspiciousHeadersFacade::validateRequestHeaders();
        return $next($request);
    }
}
