<div>
    <aside id="logo-sidebar"
        class="fixed top-0 left-0 z-40 w-64 h-screen pt-20 transition-transform -translate-x-full bg-white border-r border-gray-200 sm:translate-x-0 dark:bg-gray-800 dark:border-gray-700"
        aria-label="Sidebar">
        <div class="h-full px-3 pb-4 overflow-y-auto bg-white dark:bg-gray-800 flex flex-col justify-between">
            <ul class="space-y-2 font-medium">
                <x-nav-link href="system.settings" iconClass="ms-1" icon="gear" class="hover:bg-green-200 transition duration-75">
                    {{ __('System settings') }}
                </x-nav-link>

                {{-- <x-nav-link href="default.dashboard" iconClass="ms-1" class="hover:bg-green-200 transition duration-75">
                    <x-slot:icon><i class="fa-solid fa-chart-pie"></i></x-slot>
                    {{ __('Dashboard') }}
                </x-nav-link>

                <x-nav-link href="user.settings" iconClass="ms-1" class="hover:bg-green-200 transition duration-75">
                    <x-slot:icon><i class="fa-solid fa-user-shield"></i></x-slot>
                    {{ __('Profile settings') }}
                </x-nav-link> --}}

                {{-- @can('viewAny', $sheildMaster)
                    <x-nav-link href="sheilds.masters" iconClass="ms-1" class="hover:bg-green-200 transition duration-75">
                        <x-slot:icon><i class="fa-solid fa-gear"></i></x-slot>
                        {{ __('System settings') }}
                    </x-nav-link>
                @endcan

                @can('viewAny', $user)
                    <x-nav-link href="users" iconClass="ms-1" class="hover:bg-green-200 transition duration-75">
                        <x-slot:icon><i class="fa-solid fa-users"></i></x-slot>
                        {{ __('Users management') }}
                    </x-nav-link>
                @endcan

                @can('viewAny', $user)
                    <x-nav-link href="change.logs" iconClass="ms-1" class="hover:bg-green-200 transition duration-75">
                        <x-slot:icon><i class="fa-solid fa-clock-rotate-left"></i></x-slot>
                        {{ __('System logs') }}
                    </x-nav-link>
                @endcan --}}
            </ul>
            {{-- <div>
                @auth
                    <form action="{{ route('logout') }}" method="POST">
                        @csrf
                        <button type="submit"
                            class="w-full md:ms-1 mb-1 p-2 text-xs md:text-base font-medium text-center flex space-x-1 cursor-pointer items-center text-red-50
                            bg-red-700 rounded-lg hover:bg-red-800 focus:ring-4  focus:outline-none focus:ring-blue-300">
                            <i class="fa-solid fa-power-off me-2"></i>
                            {{ __('Logout') }}
                        </button>
                    </form>
                @endauth
            </div> --}}
        </div>
    </aside>
</div>
