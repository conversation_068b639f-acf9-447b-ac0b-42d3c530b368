<?php

namespace App\Facades\Repos\AllowedExternalIps;

use Illuminate\Support\Facades\Facade;
use App\Contracts\Repos\AllowedExternalIpsRepos\ForgetCachedCountAllowedExternalIpsRepositoryInterface;

class ForgetCachedCountAllowedExternalIpsFacade extends Facade
{
    protected static function getFacadeAccessor(): string
    {
        return ForgetCachedCountAllowedExternalIpsRepositoryInterface::class;
    }
}
