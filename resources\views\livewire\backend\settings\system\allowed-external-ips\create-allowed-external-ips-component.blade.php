<div>
    <x-button class="bg-slate-600 hover:bg-green-600" wire:click="openModalToCreateAllowedExternalIp"
        target="openModalToCreateAllowedExternalIp" icon="circle-plus">
        {{ __('Create External IP') }}
    </x-button>

    <x-dialog-modal wire:model="revealCreateAllowedExternalIpModal" :maxWidth="'xl'">
        <x-slot name="content">
            <x-divider nameClass="text-blue-400">
                {{ __('CREATE ALLOWED EXTERNAL IP') }}
            </x-divider>

            <x-road-access />
            <x-rate-limiter-message :message="$this->roadAccessForm->road_access_rate_limiter" />

            @if (!$this->roadAccessForm->road_access_rate_limiter)
                <x-lable-input-error id="form.ip_address" type="text" label="IP address" model="form.ip_address"
                    inputType="input" value="{{ $form->ip_address }}" :onEnter="$form->getCreateAllowedExternalIpEvent()"
                    placeholder="Enter External IP address...." modelModifier="live.debounce.400ms" inputClass="w-full"
                    icon="globe" iconClass="ps-2.5" />

                <x-lable-input-error id="form.description" type="text" label="Description" model="form.description"
                    inputType="textarea" value="{{ $form->description }}" :onEnter="$form->getCreateAllowedExternalIpEvent()"
                    placeholder="Enter description external IP address...." modelModifier="live.debounce.500ms" inputClass="w-full"
                    parentClass="mt-3" />
            @endif



            <div class="sm:flex items-center mt-4">
                <x-button class=" bg-red-500 hover:bg-red-600"
                    wire:click="$toggle('revealCreateAllowedExternalIpModal')"
                    target="revealCreateAllowedExternalIpModal" icon='ban'>
                    {{ __('Cancel') }}
                </x-button>

                @if (!$this->roadAccessForm->road_access_rate_limiter)
                    <x-button class="ms-3  bg-green-500 hover:bg-green-600" wire:click="createAllowedExternalIp"
                        target="createAllowedExternalIp" :disabled="$form->hasErrors()" icon='cloud-arrow-up'>
                        {{ __('Create') }}
                    </x-button>
                @endif
                <x-action-message class="sm:ms-3 mt-2 sm:mt-0" on="allowedExternalIpCreated">
                    {{ __('Allowed external IP created successfully.') }}
                </x-action-message>

                <x-action-message class="sm:ms-3 mt-2 sm:mt-0 bg-red-600 text-red-50"
                    on="allowedExternalIpCreationFailed">
                    {{ __('Error occured while trying to create allowed external IP') }}
                </x-action-message>
            </div>
        </x-slot>
    </x-dialog-modal>
</div>
