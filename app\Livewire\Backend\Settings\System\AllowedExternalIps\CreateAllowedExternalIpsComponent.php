<?php

namespace App\Livewire\Backend\Settings\System\AllowedExternalIps;


use Livewire\Component;
use App\DTOs\AllowedExternalIpDTO;
use App\Facades\Securities\HoneypotFacade;
use App\Livewire\Forms\Securities\RoadAccessForm;
use App\Facades\Securities\OriginHeaderValidatorFacade;
use App\Facades\AllowedExternalIps\CreateAllowedExternalIpFacade;
use App\Livewire\Forms\AllowedExternalIps\AllowedExternalIpsForm;

class CreateAllowedExternalIpsComponent extends Component
{
    public AllowedExternalIpsForm $form;
    public RoadAccessForm $roadAccessForm;


    public $revealCreateAllowedExternalIpModal = false;

    public function openModalToCreateAllowedExternalIp()
    {
        $this->resetErrorBag();
        $this->form->resetForm();
        $this->roadAccessForm->generateTrackingTime();
        if ($this->roadAccessForm->checkUserAttempts('CreateAllowedExternalIp:' . request()->ip(), 6, 300)) {return;}
        $this->revealCreateAllowedExternalIpModal = true;
    }

    public function createAllowedExternalIp()
    {
        $validated = $this->form->validate();
        if ($this->roadAccessForm->checkUserAttempts('CreateAllowedExternalIp:' . request()->ip(), 6, 300)) {return;}

        OriginHeaderValidatorFacade::validate();
        HoneypotFacade::inspect($this->roadAccessForm);
        $dto = new AllowedExternalIpDTO(...$validated);

        try {
            CreateAllowedExternalIpFacade::execute($dto);
            $this->form->resetForm();
            $this->dispatch('allowedExternalIpCreated');
        } catch (\Throwable $th) {
            $this->dispatch('allowedExternalIpCreationFailed');
        }
    }

    public function render()
    {
        return view('livewire.backend.settings.system.allowed-external-ips.create-allowed-external-ips-component');
    }
}
