<div>
    <x-action-buttons :id="$role->id" editAction="openModalToUpdateRole" />

    <x-dialog-modal wire:model="revealUpdateRoleModal" :maxWidth="'xl'">
        <x-slot name="content">
            <x-divider nameClass="text-blue-400">
                {{ __('UPDATE A ROLE') }}
            </x-divider>

            <x-road-access />
            <x-rate-limiter-message :message="$this->roadAccessForm->road_access_rate_limiter" />

            @if (!$this->roadAccessForm->road_access_rate_limiter)
                <x-lable-input-error id="form.name" type="text" label="Role name" model="form.name" inputType="input"
                    value="{{ $form->name }}" :onEnter="$form->getUpdateRoleEvent()" placeholder="Enter role name...."
                    modelModifier="live.debounce.400ms" inputClass="w-full" icon="user-tag" iconClass="ps-2.5" />

                <x-lable-input-error id="form.description" type="text" label="Role description"
                    model="form.description" inputType="textarea" value="{{ $form->description }}" :onEnter="$form->getUpdateRoleEvent()"
                    placeholder="Enter role description...." modelModifier="live.debounce.500ms" inputClass="w-full"
                    parentClass="mt-3" />

                <x-lable-select-error parentClass="mt-2" labelClass="w-full text-slate-300"
                    selectClass="font-bold text-green-800" id="form.type" label="Role type" model="form.type"
                    placeholder="Select role type" :options="$this->roleTypeOptions" :value="$form->type"
                    modelModifier="live.debounce.500ms" />

                {{-- <x-notice class="py-1 mt-2 text-slate-300 " noticeClass="uppercase text-xs font-semibold">
                <x-slot:notice>Is it a platform Role?</x-slot>
                <x-toggle-switch labelClass="mt-2" :checked="$form->is_platform" toggle="toggleSetting" />
            </x-notice> --}}
            @endif


            <div class="sm:flex items-center mt-4">
                <x-button class=" bg-red-500 hover:bg-red-600" wire:click="$toggle('revealUpdateRoleModal')"
                    target="revealUpdateRoleModal" icon='ban'>
                    {{ __('Cancel') }}
                </x-button>

                @if (!$this->roadAccessForm->road_access_rate_limiter)
                    <x-button class="ms-3  bg-green-500 hover:bg-green-600" wire:click="updateRole" target="updateRole"
                        :disabled="$form->hasErrors()" icon='cloud-arrow-up'>
                        {{ __('Update') }}
                    </x-button>
                @endif

                <x-action-message class="sm:ms-3 mt-2 sm:mt-0" on="roleUpdated">
                    {{ __('Role updated successfully.') }}
                </x-action-message>

                <x-action-message class="sm:ms-3 mt-2 sm:mt-0 bg-red-600 text-red-50" on="roleUpdateFailed">
                    {{ __('Error occured while trying to update role') }}
                </x-action-message>
            </div>
        </x-slot>
    </x-dialog-modal>




</div>
