<?php

namespace Tests\Feature\Livewire\Backend\Settings\User;

use App\Livewire\Backend\Settings\User\UserSettingsComponent;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Livewire\Livewire;
use Tests\TestCase;

class UserSettingsComponentTest extends TestCase
{
    public function test_renders_successfully()
    {
        Livewire::test(UserSettingsComponent::class)
            ->assertStatus(200);
    }
}
