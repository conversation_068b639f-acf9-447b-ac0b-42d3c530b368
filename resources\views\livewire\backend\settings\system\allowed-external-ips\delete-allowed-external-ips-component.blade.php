<div>
    <x-action-buttons :id="$allowedExternalIp->id" deleteAction="openModalToDeleteAllowedExternalIp" />

    <x-dialog-modal wire:model="revealDeleteAllowedExternalIpModal" :maxWidth="'xl'">
        <x-slot name="content">
            <x-divider nameClass="text-red-400">
                {{ __('DELETE ALLOWED EXTERNAL IP') }}
            </x-divider>

            <x-road-access />
            <x-rate-limiter-message :message="$this->roadAccessForm->road_access_rate_limiter" />

            @if (!$this->roadAccessForm->road_access_rate_limiter)
                <x-notice class="text-red-50 bg-red-700  border mt-1 py-1" noticeClass="uppercase text-xs">
                    <x-slot:notice>{{ __('Delete allowed external IP') }}</x-slot>
                    {{ 'Are you sure you want to delete ' . $allowedExternalIp->ip_address . ' ip address?' }}
                </x-notice>
            @endif

            <div class="sm:flex items-center mt-4">
                <x-button class=" bg-red-500 hover:bg-red-600" wire:click="$toggle('revealDeleteAllowedExternalIpModal')"
                    target="revealDeleteAllowedExternalIpModal" icon='ban'>
                    {{ __('Cancel') }}
                </x-button>

                @if (!$this->roadAccessForm->road_access_rate_limiter)
                    <x-button class="ms-3  bg-green-500 hover:bg-green-600" wire:click="deleteAllowedExternalIp"
                        target="deleteAllowedExternalIp" icon='trash'>
                        {{ __('Delete') }}
                    </x-button>
                @endif

                <x-action-message class="sm:ms-3 mt-2 sm:mt-0 bg-red-600 text-red-50"
                    on="allowedExternalIpDeleteFailed">
                    {{ __('Error occured while trying to delete allowed external IP') }}
                </x-action-message>
            </div>
        </x-slot>
    </x-dialog-modal>
</div>
