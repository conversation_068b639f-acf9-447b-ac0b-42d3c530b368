@props([
    'id',
    'title' => 'No heading',
    'buttonClass' => '',
    'icon' => 'question',
    'iconClass' => '',
    'contentClass' => '',
    'expanded' => 'false',
    'hiddenContent' => 'true',
])

<h2 id="accordion-collapse-heading-{{ $id }}">
    <button type="button"
        class="text-slate-100 border  border-slate-500  flex items-center justify-between w-full gap-3 p-3 py-3
        font-medium duration-300 ease-in-out md:px-5  {{ $buttonClass }}"
        data-accordion-target="#accordion-collapse-body-{{ $id }}" aria-expanded="{{ $expanded }}"
        aria-controls="accordion-collapse-body-{{ $id }}">

        <span class="text-sm font-bold uppercase md:text-base ">
            @if ($icon)
                <span class="me-2 text-lg {{ $iconClass }}"><i class="fa-solid fa-{{ $icon }}"></i></span>
            @endif
            {{ $title }}
        </span>
        <i data-accordion-icon class="fa-solid fa-chevron-up rotate-180" aria-hidden="false"></i>
    </button>
</h2>
<div wire:ignore.self id="accordion-collapse-body-{{ $id }}"
    class="border border-slate-600 hidden p-4 {{ $contentClass }}"
    aria-labelledby="accordion-collapse-heading-{{ $id }}">
    {{ $slot }}
</div>

<script>
    document.addEventListener('DOMContentLoaded', () => {
        document.querySelectorAll('[data-accordion="collapse"] button').forEach(button => {
            button.addEventListener('click', () => {
                window.history.replaceState({}, document.title, window.location.pathname);
            });
        });
    });
</script>
