<?php

namespace App\Livewire\Backend\Settings\System\BlockedIps;

use Livewire\Component;
use Livewire\Attributes\Locked;

class ViewBlockedIpsComponent extends Component
{
    #[Locked]
    public $blockedIp;
    public $revealViewBlockedIpModal = false;

    public function openModalToViewBlockedIp()
    {
        $this->revealViewBlockedIpModal = true;
    }

    public function render()
    {
        return view('livewire.backend.settings.system.blocked-ips.view-blocked-ips-component');
    }
}
