<?php

namespace Tests\Feature\Livewire\Backend\Settings\System\AllowedMails;

use App\Livewire\Backend\Settings\System\AllowedMails\DeleteAllowedMailsComponent;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Livewire\Livewire;
use Tests\TestCase;

class DeleteAllowedMailsComponentTest extends TestCase
{
    public function test_renders_successfully()
    {
        Livewire::test(DeleteAllowedMailsComponent::class)
            ->assertStatus(200);
    }
}
