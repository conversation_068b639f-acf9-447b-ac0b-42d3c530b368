<?php

namespace App\Services\BlockedIps;

use App\Models\BlockedIp;
use App\DTOs\BlockedIpDTO;
use App\Actions\BlockedIps\CreateBlockedIpAction;
use App\Contracts\BlockedIps\CreateBlockedIpServiceInterface;

class CreateBlockedIpService implements CreateBlockedIpServiceInterface
{
    public function execute(BlockedIpDTO $dto): BlockedIp
    {
        return CreateBlockedIpAction::handle($dto->toArray());
    }
}
