<?php

namespace App\Livewire\Forms\AllowedExternalIps;

use Livewire\Form;
use App\Models\AllowedExternalIp;
use App\Rules\GibberishRule;
use Livewire\Attributes\Validate;
use Illuminate\Validation\Rule;

class AllowedExternalIpsForm extends Form
{
    #[Validate]
    public $ip_address;
    #[Validate]
    public $description;

    public ?AllowedExternalIp $selectedAllowedExternalIp  = null;

    public function rules(): array
    {
        return [
            'ip_address' => array_filter([
                'required',
                'string',
                'min:3',
                'max:100',
                new GibberishRule('ip_address', 3, 3),
                Rule::unique('allowed_external_ips')->ignore($this->selectedAllowedExternalIp)
            ]),
            'description' => array_filter([
                'required',
                'string',
                'min:5',
                'max:500',
                new GibberishRule('description', 3, 2)
            ]),
        ];
    }

    public function resetForm()
    {
        $this->reset(['ip_address', 'description']);
    }
    public function hasErrors(): bool
    {
        return $this->getErrorBag()->isNotEmpty() || empty($this->ip_address) || empty($this->description);
    }
    public function getCreateAllowedExternalIpEvent(): string
    {
        return $this->hasErrors() ? '' : 'createAllowedExternalIp';
    }
    public function getUpdateAllowedExternalIpEvent(): string
    {
        return $this->hasErrors() ? '' : 'updateAllowedExternalIp';
    }

}
