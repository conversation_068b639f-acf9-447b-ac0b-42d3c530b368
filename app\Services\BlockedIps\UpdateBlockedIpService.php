<?php

namespace App\Services\BlockedIps;

use App\Models\BlockedIp;
use App\DTOs\BlockedIpDTO;
use App\Actions\BlockedIps\UpdateBlockedIpAction;
use App\Contracts\BlockedIps\UpdateBlockedIpServiceInterface;

class UpdateBlockedIpService implements UpdateBlockedIpServiceInterface
{
    public function execute(BlockedIp $blockedIp, BlockedIpDTO $dto): BlockedIp
    {
        return UpdateBlockedIpAction::handle($blockedIp, $dto->toArray());
    }
}
