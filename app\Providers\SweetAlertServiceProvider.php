<?php

namespace App\Providers;

use Livewire\Component;
use Illuminate\Support\ServiceProvider;

class SweetAlertServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        Component::macro('showSuccessSweetAlert', function ($title, $message) {
            $title = strtoupper($title); // Convert title to uppercase
            $this->js(<<<JS
                Swal.fire({
                position: 'top-center',
                icon: 'success',
                title: '$title',
                text: '$message',
                showConfirmButton: true,
                animation: true,
                })
            JS);
        });

        Component::macro('showErrorSweetAlert', function ($title, $message) {
            $title = strtoupper($title); // Convert title to uppercase
            $this->js(<<<JS
        Swal.fire({
        position: 'top-center',
        icon: 'error',
        title: '$title',
        text: '$message',
        showConfirmButton: true,
        animation: true,
        })
        JS);
        });


        Component::macro('showInfoSweetAlert', function ($title, $message) {
            $title = strtoupper($title); // Convert title to uppercase
            $this->js(<<<JS
        Swal.fire({
        position: 'top-center',
        icon: 'info',
        title: '$title',
        text: '$message',
        showConfirmButton: true,
        animation: true,
        })
        JS);
        });


        Component::macro('showWarningSweetAlert', function ($title, $message) {
        $title = strtoupper($title); // Convert title to uppercase
        $this->js(<<<JS
        Swal.fire({
        position: 'top-center',
        icon: 'warning',
        title: '$title',
        text: '$message',
        showConfirmButton: true,
        animation: true,
        })
        JS);
        });
    }
}
