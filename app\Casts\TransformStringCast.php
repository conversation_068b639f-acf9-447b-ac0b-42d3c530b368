<?php

namespace App\Casts;

use Illuminate\Contracts\Database\Eloquent\CastsAttributes;
use Illuminate\Database\Eloquent\Model;

class TransformStringCast implements CastsAttributes
{
    public function __construct(protected string $type = 'ucfirst') {}

    /**
     * Cast the given value.
     *
     * @param  array<string, mixed>  $attributes
     */
    public function get(Model $model, string $key, mixed $value, array $attributes): mixed
    {

        return match (strtolower($this->type)) {
            'ucwords'      => ucwords($value),
            'strtolower'   => strtolower($value),
            'strtoupper'   => strtoupper($value),
            'lcfirst'      => lcfirst($value),
            'capitalize'   => ucfirst($value),
            'trim'         => trim($value),
            'uppercase'    => strtoupper($value),
            'lowercase'    => strtolower($value),
            default        => $value,
        };
    }

    /**
     * Prepare the given value for storage.
     *
     * @param  array<string, mixed>  $attributes
     */
    public function set(Model $model, string $key, mixed $value, array $attributes): mixed
    {
        return $value;
    }
}
