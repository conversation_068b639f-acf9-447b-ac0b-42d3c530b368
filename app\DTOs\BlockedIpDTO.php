<?php

namespace App\DTOs;

class BlockedIpDTO
{
    public function __construct(
        public readonly ?string $ip_address = null,
        public readonly ?string $reason = null,

    ) {}

    public function toArray(): array
    {
        return array_filter([
            'ip_address' => $this->ip_address,
            'reason' => $this->reason,
        ], fn($value) => !is_null($value));
    }
}
