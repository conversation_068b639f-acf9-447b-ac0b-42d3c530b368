<?php

namespace App\Rules;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class GibberishRule implements ValidationRule
{

    protected string $field;
    protected int $maxRepeat;
    protected int $maxWordRepeat;

    public function __construct(string $field = 'field', int $maxRepeat = 2, int $maxWordRepeat = 2)
    {
        $this->field = $field;
        $this->maxRepeat = $maxRepeat;
        $this->maxWordRepeat = $maxWordRepeat;
    }

    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        // 2. Reject if excessive vowels appear in a row (e.g., "aeiou")
        if (preg_match('/[aeiou]{4,}/i', $value)) {
            $fail("The {$this->field} appears to contain gibberish");
            return;
        }

        // 2. Reject if excessive consonants appear in a row (e.g., "jdfkjd")
        if (preg_match('/[bcdfghjklmnpqrstvwxyz]{6,}/i', $value)) {
            $fail("The {$this->field} appears to contain gibberish");
            return;
        }

        // 3. Reject if same character chunks are repeated (e.g., "hahahahahaha")
        if (preg_match('/(\w{2,})\1{' . $this->maxRepeat . ',}/i', $value)) {
            $fail("The {$this->field} appears to contain gibberish");
            return;
        }

        // 3. Reject if same word appears multiple times (e.g., "hello hello hello")
        if (preg_match('/\b(\w+)\b(?:.*\b\1\b){' . $this->maxWordRepeat . ',}/i', $value)) {
            $fail("The {$this->field} repeats the same word too many times.");
            return;
        }
    }
}
