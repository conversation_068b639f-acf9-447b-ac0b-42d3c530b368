<?php

namespace App\Contracts\AllowedExternalIps;

use App\Models\AllowedExternalIp;
use App\DTOs\AllowedExternalIpDTO;
use App\Services\AllowedExternalIps\UpdateAllowedExternalIpService;
use Illuminate\Container\Attributes\Bind;

#[Bind(UpdateAllowedExternalIpService::class)]
interface UpdateAllowedExternalIpServiceInterface
{
    public function execute(AllowedExternalIp $selectedAllowedExternalIp, AllowedExternalIpDTO $dto): AllowedExternalIp;
}
