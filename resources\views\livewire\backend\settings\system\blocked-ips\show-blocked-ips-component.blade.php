<div>
    @php
        $tablePagination = $this->blockedIps->isNotEmpty() && $this->totalBlockedIps > 5 ? true : false;
        $blockedIpsAvailable = $tablePagination;
        $rowNumber = 0; // Initialize row number
    @endphp
    <div>
        <livewire:backend.settings.system.blocked-ips.create-blocked-ips-component />

        {{-- Search --}}
        <x-search :results="$blockedIpsAvailable" searchProperty="blockedIpsSearch" searchPlaceholder="Search by blocked IPs...."
            formWidth="max-w-full my-3" />

        {{-- Table --}}
        <x-table :columns="[
            [
                'label' => '#',
                'headerClass' => 'border-slate-600',
            ],
            [
                'label' => 'IP Address',
                'headerClass' => 'border-l border-slate-600 w-32',
            ],
            [
                'label' => 'reason',
                'headerClass' => 'border-l border-slate-600 line-clamp-1',
            ],
            [
                'label' => 'Actions',
                'headerClass' => 'border-l border-slate-600 w-20',
            ],
        ]" :data="$this->blockedIps" captionClass="text-slate-200" sortByMethodName="sortBlockedIpsBy"
            :results="$blockedIpsAvailable" maxScrollHeight="max-h-[400px] hidden lg:block pr-1"
            theadClass="bg-red-700 border-l border-red-700 text-red-50" subtheadClass="px-2"
            rowClass="border-b hover:bg-slate-700 hover:text-slate-100 hover:border-y-2 hover:border-slate-600 "
            tableClass="border-2 border-slate-700">
            @foreach ($this->blockedIps as $blockedIp)
                <tr wire:key="{{ 'blockedIp-' . $blockedIp->id }}"
                    class="border-b bg-slate-800 hover:bg-slate-700 border-slate-700">
                    <td class="px-2 py-1  text-slate-200">
                        {{ $loop->iteration }}
                    </td>
                    <td class="border-l border-slate-600 px-2 py-1 text-slate-200 w-32">
                        {{ $blockedIp->ip_address }}
                    </td>
                    <td class="border-l border-slate-600 px-2 py-1 text-slate-200  line-clamp-1">
                        {{ $blockedIp->reason }}
                    </td>
                    <td class="border-l border-slate-600 px-2 py-1 w-20">
                        <span class="flex justify-end items-center space-x-1">
                            {{-- Action buttons --}}
                            <livewire:backend.settings.system.blocked-ips.view-blocked-ips-component :$blockedIp
                                :key="'view-blockedIp-' . $blockedIp->id">
                                <livewire:backend.settings.system.blocked-ips.update-blocked-ips-component :$blockedIp
                                    :key="'update-blockedIp-' . $blockedIp->id">
                                    <livewire:backend.settings.system.blocked-ips.delete-blocked-ips-component
                                        :$blockedIp :key="'delete-blockedIp-' . $blockedIp->id">
                        </span>
                    </td>
                </tr>
            @endforeach
        </x-table>


        {{-- Table List --}}
        <x-table-list :data="$this->blockedIps" captionMobileClass="text-slate-200"
            maxScrollHeight="max-h-[500px] lg:hidden pr-1">
            @foreach ($this->blockedIps as $blockedIp)
                @php
                    $rowNumber++;
                @endphp
                <div wire:key="{{ 'blockedIp-2-' . $blockedIp->id }}"
                    class="p-2 duration-300 ease-in-out rounded-md shadow bg-slate-900 text-slate-50 hover:bg-slate-600 hover:shadow-lg">
                    <div class="flex justify-between items-center mb-1 pr-1">
                        <span class="text-sm text-slate-100">Blocked Ip:</span>
                        <span class="text-sm text-slate-100">{{ $blockedIp->ip_address }}</span>
                    </div>
                    <div class="mb-1 block bg-red-800 rounded px-1.5 py-1">
                        <div class=" text-amber-200">Reason for blocking:</div>
                        <div class="text-sm text-slate-200 line-clamp-2">{{ $blockedIp->reason }}</div>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="flex justify-end items-center space-x-1">
                            {{-- Action buttons --}}
                            <livewire:backend.settings.system.blockedIps.view-blockedIps-component :$blockedIp
                                :key="'view-2-blockedIp-' . $blockedIp->id">
                                <livewire:backend.settings.system.blockedIps.update-blockedIps-component :$blockedIp
                                    :key="'update-2-blockedIp-' . $blockedIp->id">
                                    <livewire:backend.settings.system.blockedIps.delete-blockedIps-component :$blockedIp
                                        :key="'delete-2-blockedIp-' . $blockedIp->id">
                        </span>

                        @if ($rowNumber >= 1)
                            <span
                                class="text-right rounded-full px-2 bg-slate-600 text-slate-50 text-xs">{{ $rowNumber }}</span>
                        @endif
                    </div>
                </div>
            @endforeach
        </x-table-list>

        {{-- Pagination --}}
        <x-paginator :pages="$this->blockedIpsPages" :totalRecords="$this->totalBlockedIps" selectClass="text-slate-500"
            paginateRecords="blockedIpsPaginate" :results="$blockedIpsAvailable" :paginationEnabled="$tablePagination" />
    </div>
</div>
