<?php

namespace Tests\Feature\Livewire\Backend\Settings\System\Roles;

use App\Livewire\Backend\Settings\System\Roles\UpdateRolesComponent;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Livewire\Livewire;
use Tests\TestCase;

class UpdateRolesComponentTest extends TestCase
{
    public function test_renders_successfully()
    {
        Livewire::test(UpdateRolesComponent::class)
            ->assertStatus(200);
    }
}
