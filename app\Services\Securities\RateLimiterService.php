<?php

namespace App\Services\Securities;

use Illuminate\Support\Facades\RateLimiter;
use App\Contracts\Securities\RateLimiterServiceInterface;


class RateLimiterService implements RateLimiterServiceInterface
{
    public function check(string $key, int $maxAttempts, int $decaySeconds = 60): ?string
    {
        // Step 1: Check if user exceeded max attempts
        if (RateLimiter::tooManyAttempts($key, $maxAttempts)) {
            $seconds = RateLimiter::availableIn($key);
            return "Too many requests. You may try again in {$seconds} seconds.";
        }

        // Step 2: Record the attempt
        RateLimiter::hit($key, $decaySeconds);

        return null; // null means allowed
    }
}
