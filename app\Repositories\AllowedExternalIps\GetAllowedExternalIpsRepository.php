<?php

namespace App\Repositories\AllowedExternalIps;

use App\Models\AllowedExternalIp;
use Illuminate\Support\Collection;
use App\Contracts\Repos\AllowedExternalIpsRepos\GetAllowedExternalIpsRepositoryInterface;

final class GetAllowedExternalIpsRepository implements GetAllowedExternalIpsRepositoryInterface
{
    public function handle(int $limit = 10, array $selectedColumns = ['*']): Collection
    {
        return AllowedExternalIp::select($selectedColumns)
            ->orderByIdDesc()
            ->limit($limit)
            ->get();
    }
}
