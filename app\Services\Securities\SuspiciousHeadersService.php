<?php

namespace App\Services\Securities;

use App\Models\BlockedIp;
use App\DTOs\BlockedIpDTO;
use App\Models\UntrustedHeader;
use App\DTOs\UntrustedHeaderDTO;
use Illuminate\Support\Facades\Request;
use App\Facades\BlockedIps\CreateBlockedIpFacade;
use App\Facades\UntrustedHeaders\CreateUntrustedHeaderFacade;
use App\Contracts\Securities\SuspiciousHeadersServiceInterface;
use App\Facades\Repos\UntrustedHeaders\GetUntrustedHeadersFacade;

class SuspiciousHeadersService implements SuspiciousHeadersServiceInterface
{
    public function validateRequestHeaders(): void
    {
        $ip = Request::ip();
        $compulsoryHeaders = ['user-agent', 'accept-language'];

        foreach ($compulsoryHeaders as $header) {
            if (!request()->hasHeader($header)) {
                $data = [
                    'ip' => $ip,
                    'reason' => "Caught by suspicious headers validator. Missing mandatory header: $header",
                ];
                $dto = new BlockedIpDTO(...$data);
                CreateBlockedIpFacade::execute($dto);
                abort(403, 'FORBIDDEN');
            }
        }

        $headers = GetUntrustedHeadersFacade::handle(50000, ['header_type', 'pattern']);
        $grouped = $headers->groupBy('header_type');

        $headerPatterns = $grouped->map(function ($items) {
            return $items->pluck('pattern')->toArray();
        })->toArray();

        foreach ($headerPatterns as $headerName => $patterns) {
            $headerValue = Request::header($headerName);
            // Compare against suspicious patterns
            foreach ($patterns as $pattern) {
                if (str_contains(strtolower($headerValue), strtolower($pattern))) {
                    $values = [
                        'header_type' => $headerName,
                        'pattern' => $headerValue,
                        'description' => 'Auto-logged suspicious header from request',
                    ];
                    $dto = new UntrustedHeaderDTO(...$values);
                    CreateUntrustedHeaderFacade::execute($dto);

                    $data = [
                        'ip' => $ip,
                        'reason' => "Caught by suspicious headers validator. Suspicious header detected: $headerName",
                    ];
                    $dto = new BlockedIpDTO(...$data);
                    CreateBlockedIpFacade::execute($dto);
                    abort(403, 'FORBIDDEN');
                }
            }
        }
    }
}
