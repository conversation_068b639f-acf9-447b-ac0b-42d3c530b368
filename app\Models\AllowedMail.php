<?php

namespace App\Models;

use App\Casts\TransformStringCast;
use App\Builders\AllowedMailBuilder;
use App\Traits\Defaults\HasFilterables;
use App\Traits\Defaults\HasSearchables;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Builder;
use App\Enums\AllowedMails\AllowedMailStatusEnum;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class AllowedMail extends Model
{
    /** @use HasFactory<\Database\Factories\AllowedMailFactory> */
    use HasFactory;
    use HasSearchables;
    use HasFilterables;


    protected $fillable = [
        'email',
        'status',
        'description',
    ];

    protected $casts = [
        'id' => 'integer',
        'email' => TransformStringCast::class . ':strtolower',
        'status' => AllowedMailStatusEnum::class,
        'description' => TransformStringCast::class . ':ucfirst',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    protected $searchable = [
        'email',
        'description',
    ];

    protected $filterable = [
        'filterMailsByStatus' => 'status',
    ];

    public function newEloquentBuilder($query): Builder
    {
        return new AllowedMailBuilder($query);
    }
}
