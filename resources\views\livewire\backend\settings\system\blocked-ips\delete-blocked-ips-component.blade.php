<div>
    <x-action-buttons :id="$blockedIp->id" deleteAction="openModalToDeleteBlockedIp" />

    <x-dialog-modal wire:model="revealDeleteBlockedIpModal" :maxWidth="'xl'">
        <x-slot name="content">
            <x-divider nameClass="text-red-400">
                {{ __('DELETE BLOCKED IP') }}
            </x-divider>

            <x-road-access />
            <x-rate-limiter-message :message="$this->roadAccessForm->road_access_rate_limiter" />

            <x-notice class="text-red-50 bg-red-700  border mt-1 py-1" noticeClass="uppercase text-xs">
                <x-slot:notice>{{ __('Delete IP address to block') }}</x-slot>
                {{ 'Are you sure you want to delete ' . $blockedIp->ip_address . ' ip address?' }}
            </x-notice>

            <div class="sm:flex items-center mt-4">
                <x-button class=" bg-red-500 hover:bg-red-600" wire:click="$toggle('revealDeleteBlockedIpModal')"
                    target="revealDeleteBlockedIpModal" icon='ban'>
                    {{ __('Cancel') }}
                </x-button>

                @if (!$this->roadAccessForm->road_access_rate_limiter)
                    <x-button class="ms-3  bg-green-500 hover:bg-green-600" wire:click="deleteBlockedIp"
                        target="deleteBlockedIp" icon='trash'>
                        {{ __('Delete') }}
                    </x-button>
                @endif

                <x-action-message class="sm:ms-3 mt-2 sm:mt-0" on="blockedIpDeleted">
                    {{ __('Blocked ip address deleted successfully.') }}
                </x-action-message>

                <x-action-message class="sm:ms-3 mt-2 sm:mt-0 bg-red-600 text-red-50" on="blockedIpDeleteFailed">
                    {{ __('Error occured while trying to delete blocked IP address') }}
                </x-action-message>
            </div>
        </x-slot>
    </x-dialog-modal>
</div>
