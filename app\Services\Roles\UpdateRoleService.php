<?php

namespace App\Services\Roles;

use App\Models\Role;
use App\DTOs\RoleDTO;
use App\Actions\Roles\UpdateRoleAction;
use App\Contracts\Roles\UpdateRoleServiceInterface;

class UpdateRoleService implements UpdateRoleServiceInterface
{
    public function execute(Role $selectedRole, RoleDTO $dto): Role
    {
        // Assuming you have an UpdateRoleAction that handles the update logic
        return UpdateRoleAction::handle($selectedRole, $dto->toArray());
    }
}
