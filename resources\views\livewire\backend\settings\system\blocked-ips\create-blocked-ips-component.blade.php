<div>
    <x-button class="bg-slate-600 hover:bg-green-600" wire:click="openModalToCreateBlockedIp"
        target="openModalToCreateBlockedIp" icon="circle-plus">
        {{ __('Add IP to Block') }}
    </x-button>

    <x-dialog-modal wire:model="revealCreateBlockedIpModal" :maxWidth="'xl'">
        <x-slot name="content">
            <x-divider nameClass="text-blue-400">
                {{ __('CREATE IP TO BLOCK') }}
            </x-divider>

            <x-road-access />
            <x-rate-limiter-message :message="$this->roadAccessForm->road_access_rate_limiter" />

            @if (!$this->roadAccessForm->road_access_rate_limiter)
                <x-lable-input-error id="form.ip_address" type="text" label="IP address" model="form.ip_address" inputType="input"
                    value="{{ $form->ip_address }}" :onEnter="$form->getCreateBlockedIpEvent()" placeholder="Enter IP to block...."
                    modelModifier="live.debounce.400ms" inputClass="w-full" icon="globe" iconClass="ps-2.5" />

                <x-lable-input-error id="form.reason" type="text" label="Reason" model="form.reason"
                    inputType="textarea" value="{{ $form->reason }}" :onEnter="$form->getCreateBlockedIpEvent()"
                    placeholder="Enter reason for blocking this IP...." modelModifier="live.debounce.500ms"
                    inputClass="w-full" parentClass="mt-3" />
            @endif

            <div class="sm:flex items-center mt-4">
                <x-button class=" bg-red-500 hover:bg-red-600" wire:click="$toggle('revealCreateBlockedIpModal')"
                    target="revealCreateBlockedIpModal" icon='ban'>
                    {{ __('Cancel') }}
                </x-button>

                @if (!$this->roadAccessForm->road_access_rate_limiter)
                    <x-button class="ms-3  bg-green-500 hover:bg-green-600" wire:click="createBlockedIp"
                        target="createBlockedIp" :disabled="$form->hasErrors()" icon='cloud-arrow-up'>
                        {{ __('Create') }}
                    </x-button>
                @endif
                <x-action-message class="sm:ms-3 mt-2 sm:mt-0" on="blockedIpCreated">
                    {{ __('IP to block created successfully.') }}
                </x-action-message>

                <x-action-message class="sm:ms-3 mt-2 sm:mt-0 bg-red-600 text-red-50" on="blockedIpCreationFailed">
                    {{ __('Error occured while trying to create IP to block') }}
                </x-action-message>
            </div>
        </x-slot>
    </x-dialog-modal>
</div>
