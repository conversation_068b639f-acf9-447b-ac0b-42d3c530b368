
@props([
    'id' => null,
    'maxWidth' => '2xl',
    'clickAway' => true,       // close when clicking outside?
    'closeOnEscape' => true,   // close when pressing ESC?
    'trapFocus' => true,       // requires @alpinejs/focus
])

@php
    $wireModel = $attributes->wire('model');

    $id = $id ?? md5($wireModel ?? uniqid());

    $maxWidths = [
        'sm'  => 'sm:max-w-sm',
        'md'  => 'sm:max-w-md',
        'lg'  => 'sm:max-w-lg',
        'xl'  => 'sm:max-w-xl',
        '2xl' => 'sm:max-w-2xl',
        '3xl' => 'md:max-w-3xl',
        '4xl' => 'lg:max-w-4xl',
    ];

    $maxWidthClass = $maxWidths[$maxWidth] ?? $maxWidths['2xl'];
@endphp

<div
    x-data="{
        show: @entangle($wireModel),
        clickAway: {{ $clickAway ? 'true' : 'false' }},
        closeOnEscape: {{ $closeOnEscape ? 'true' : 'false' }},
    }"
    x-on:keydown.escape.window="if (closeOnEscape) { show = false; $dispatch('hide-spinner') }"
    x-on:close.stop="show = false; $dispatch('hide-spinner')"
    x-show="show" id="{{ $id }}" class="fixed inset-0 z-50 px-4 py-6 overflow-y-auto sm:px-0"
    style="display:none;"
    role="dialog"
    aria-modal="true"
>
    <!-- Backdrop -->
    <div
        x-show="show" class="fixed inset-0 z-40 transition-opacity"
        x-transition:enter="ease-out duration-300"
        x-transition:enter-start="opacity-0"
        x-transition:enter-end="opacity-100"
        x-transition:leave="ease-in duration-200"
        x-transition:leave-start="opacity-100"
        x-transition:leave-end="opacity-0"
        x-on:click="if (clickAway) show = false"
    >
        <div class="absolute inset-0 bg-slate-900/80"></div>
    </div>

    <!-- Modal panel -->
    <div
        x-show="show"
        class="relative z-50 mb-6 bg-slate-700 border border-slate-700 rounded-none overflow-hidden shadow-xl transform transition-all sm:w-full {{ $maxWidthClass }} sm:mx-auto"
        x-on:click.stop
        @if ($trapFocus)
            x-trap.inert.noscroll="show"
        @endif
        x-transition:enter="ease-out duration-300"
        x-transition:enter-start="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
        x-transition:enter-end="opacity-100 translate-y-0 sm:scale-100"
        x-transition:leave="ease-in duration-200"
        x-transition:leave-start="opacity-100 translate-y-0 sm:scale-100"
        x-transition:leave-end="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
    >
        {{ $slot }}
    </div>
</div>






{{-- @props(['id', 'maxWidth'])

@php
$id = $id ?? md5($attributes->wire('model'));

$maxWidth = [
    'sm' => 'sm:max-w-sm',
    'md' => 'sm:max-w-md',
    'lg' => 'sm:max-w-lg',
    'xl' => 'sm:max-w-xl',
    '2xl' => 'sm:max-w-2xl',
    '3xl' => 'md:max-w-3xl',
    '4xl' => 'lg:max-w-4xl',
][$maxWidth ?? '2xl'];
@endphp

<div
    x-data="{ show: @entangle($attributes->wire('model')) }"
    x-on:close.stop="show = false; $dispatch('hide-spinner')"
    x-on:keydown.escape.window="show = false; $dispatch('hide-spinner')"
    x-show="show"
    id="{{ $id }}"

    class="fixed inset-0 z-50 px-4 py-6 overflow-y-auto jetstream-modal sm:px-0"
    style="display: none;"
>
    <div x-show="show" class="fixed inset-0 transition-all transform" x-on:click="show = false" x-transition:enter="ease-out duration-300"
                    x-transition:enter-start="opacity-0"
                    x-transition:enter-end="opacity-100"
                    x-transition:leave="ease-in duration-200"
                    x-transition:leave-start="opacity-100"
                    x-transition:leave-end="opacity-0">
        <div class="absolute inset-0 opacity-25 bg-slate-200"></div>
    </div>

    <div x-show="show" class="mb-6 z-50 bg-slate-800 rounded-none overflow-hidden shadow-xl transform transition-all sm:w-full {{ $maxWidth }} sm:mx-auto"
                    x-trap.inert.noscroll="show"
                    x-transition:enter="ease-out duration-300"
                    x-transition:enter-start="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
                    x-transition:enter-end="opacity-100 translate-y-0 sm:scale-100"
                    x-transition:leave="ease-in duration-200"
                    x-transition:leave-start="opacity-100 translate-y-0 sm:scale-100"
                    x-transition:leave-end="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95">
        {{ $slot }}
    </div>
</div> --}}
