<?php

namespace App\Traits\Defaults;

use Exception;
use Illuminate\Support\Str;
use Illuminate\Database\Eloquent\Builder;

trait HasFilterables
{
    public function scopeFilter(Builder $builder, array $filters = []): Builder
    {
        if (!property_exists($this, 'filterable') || empty($this->filterable)) {
            throw new Exception('Please define a $filterable property on the model.');
        }

        foreach ($this->filterable as $key => $column) {
            if (!array_key_exists($key, $filters) || $filters[$key] === '' || $filters[$key] === null) {
                continue;
            }


            $value = $filters[$key];

            // Support relation filters like 'roles.name'
            if (Str::contains($column, '.')) {
                $relation = Str::beforeLast($column, '.');
                $col = Str::afterLast($column, '.');

                $builder->whereHas($relation, function (Builder $query) use ($col, $value) {
                    $query->where($col, $value);
                });
            } else {
                $builder->where($column, $value);
            }
        }

        return $builder;
    }
}
