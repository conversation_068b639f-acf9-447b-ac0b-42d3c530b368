<?php

namespace App\Enums\AllowedMails;

enum AllowedMailStatusEnum: int
{
    case AUTHORISED = 1;
    case UNAUTHORISED = 2;

    public function label(): string
    {
        return match ($this) {
            self::AUTHORISED => 'Authorised',
            self::UNAUTHORISED => 'Unauthorised',
        };
    }

    public static function getMailStatusOptions(): array
    {
        $options = [];
        foreach (self::cases() as $case) {
            $options[$case->value] = $case->label();
        }
        return $options;
    }
}
