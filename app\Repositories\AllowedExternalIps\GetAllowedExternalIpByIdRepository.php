<?php

namespace App\Repositories\AllowedExternalIps;

use App\Models\AllowedExternalIp;
use App\Contracts\Repos\AllowedExternalIpsRepos\GetAllowedExternalIpByIdRepositoryInterface;

final class GetAllowedExternalIpByIdRepository implements GetAllowedExternalIpByIdRepositoryInterface
{
    public function handle(int $id, array $selectedColumns = ['*']): ?AllowedExternalIp
    {
        return AllowedExternalIp::select($selectedColumns)->find($id);
    }
}
