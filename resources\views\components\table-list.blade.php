@props([
    'data' => null,
    'maxScrollHeight' => 'max-h-[350px] lg:hidden',
    'captionMobileClass' => '',
    'containerClass' => '',
    'gridClass' => 'md:grid-cols-2',
])

<span class="md:hidden mb-2  text-lg font-semibold text-left rtl:text-right  {{ $captionMobileClass }}">
    {{ $caption ?? '' }}
</span>

<div class="overflow-y-auto custom-scrollbar shadow-sm {{ $maxScrollHeight }} {{ $containerClass }}">
    <div class="grid gap-2 {{ $gridClass }}">
        @if ($data->isNotEmpty())
            {{ $slot }}
        @endif
    </div>
    @if (!$data->isNotEmpty())
        <x-notice class="grid grid-cols-1 bg-amber-100 text-amber-700 py-1 border mt-1" noticeClass="uppercase text-xs">
            <x-slot:notice>{{ 'Oops!' }}</x-slot>
            {{ __('No records found in the database') }}
        </x-notice>
    @endif
</div>
