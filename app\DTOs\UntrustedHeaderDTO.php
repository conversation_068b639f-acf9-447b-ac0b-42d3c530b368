<?php

namespace App\DTOs;

class UntrustedHeaderDTO
{
    public function __construct(
        public readonly int $header_type = 1,
        public readonly ?string $pattern = null,
        public readonly ?string $description = null,

    ) {}

    public function toArray(): array
    {
        return array_filter([
            'header_type' => $this->header_type,
            'pattern' => $this->pattern,
            'description' => $this->description,

        ], fn($value) => !is_null($value));
    }
}
