<?php

namespace App\Enums\Roles;

enum RoleIdsEnum : int
{
    case GUEST = 1;
    case SUPER_ADMINISTRATOR = 2;
    case ADMINISTRATOR = 3;
    case SYSTEM_ADMINISTRATOR = 4;
    case ASSISTANT = 5;
    case EDITOR = 6;
    case SECURITY = 7;
    case SUPPORT = 8;
    case DEVELOPER = 9;
    case AUDITOR = 10;
    case ORGANISATION_ADMINISTRATOR = 11;

    public function label(): string
    {
        return match ($this) {
            self::GUEST => 'Guest',
            self::SUPER_ADMINISTRATOR => 'Super Administrator',
            self::ADMINISTRATOR => 'Administrator',
            self::SYSTEM_ADMINISTRATOR => 'System Administrator',
            self::ASSISTANT => 'Assistant',
            self::EDITOR => 'Editor',
            self::SECURITY => 'Security',
            self::SUPPORT => 'Support',
            self::DEVELOPER => 'Developer',
            self::AUDITOR => 'Auditor',
            self::ORGANISATION_ADMINISTRATOR => 'Organisation Administrator',
        };
    }

    public static function getRoleIdsOptions(): array
    {
        $options = [];

        foreach (self::cases() as $case) {
            $options[$case->value] = $case->label();
        }

        return $options;
    }
}
