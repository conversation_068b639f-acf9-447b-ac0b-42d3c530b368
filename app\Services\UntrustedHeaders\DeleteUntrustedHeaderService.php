<?php

namespace App\Services\UntrustedHeaders;

use App\Actions\UntrustedHeaders\DeleteUntrustedHeaderAction;
use App\Models\UntrustedHeader;
use App\Contracts\UntrustedHeaders\DeleteUntrustedHeaderServiceInterface;

class DeleteUntrustedHeaderService implements DeleteUntrustedHeaderServiceInterface
{
    public function execute(UntrustedHeader $selectedUntrustedHeader): bool
    {
        if (!$selectedUntrustedHeader) {
            return false;
        }
        return DeleteUntrustedHeaderAction::handle($selectedUntrustedHeader);
    }
}
