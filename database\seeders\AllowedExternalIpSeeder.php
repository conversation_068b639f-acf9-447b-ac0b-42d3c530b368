<?php

namespace Database\Seeders;

use App\Models\AllowedExternalIp;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class AllowedExternalIpSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $data = [
            ['ip_address' => '127.0.0.1', 'description' => 'Local development machine'],
            ['ip_address' => '*************', 'description' => 'Office network'],
            ['ip_address' => '************', 'description' => 'API partner'],
            ['ip_address' => '***********', 'description' => 'Test Allowed IP 1'],
            ['ip_address' => '***********', 'description' => 'Test Allowed IP 2'],
            ['ip_address' => '***********', 'description' => 'Test Allowed IP 3'],
            ['ip_address' => '***********', 'description' => 'Test Allowed IP 4'],
            ['ip_address' => '***********', 'description' => 'Test Allowed IP 5'],
            ['ip_address' => '***********', 'description' => 'Test Allowed IP 6'],
            ['ip_address' => '***********', 'description' => 'Test Allowed IP 7'],
        ];

        $insertData = [];
        foreach ($data as $row) {
            $insertData[] = [
                'ip_address' => $row['ip_address'],
                'description' => $row['description'],
                'created_at' => now(),
                'updated_at' => now(),
            ];
        }

        AllowedExternalIp::insert($insertData);
    }
}
